package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.Map;

@Schema(description = "Customer commitment data transfer object")
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerCommitmentDto extends BaseDto {

    @JsonProperty("id")
    @Schema(
            description = "Customer commitment ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer id;

    @JsonProperty("customer_email")
    @Schema(
            description = "Customer email",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String customerEmail;

    @JsonProperty("country_code")
    @Schema(
            description = "Country code",
            example = "DE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryCode;

    @JsonProperty("year")
    @Schema(
            description = "Commitment year",
            example = "2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer year;

    @JsonProperty("service_type")
    @Schema(
            description = "Service type",
            example = "EU_LICENSE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            implementation = Contract.Type.class
    )
    private Contract.Type serviceType;

    @JsonProperty("commitment")
    @Schema(
            description = "Commitment data",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Map<String, Object> commitment;

    @JsonProperty("created_at")
    @Schema(
            description = "Creation timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(
            description = "Last update timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant updatedAt;
}
