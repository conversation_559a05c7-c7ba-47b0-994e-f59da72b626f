package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.customer.Customer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

@Schema(description = "Customer data transfer object")
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerDto extends BaseDto {

    @JsonProperty("id")
    @Schema(
            description = "Customer ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer id;

    @JsonProperty("type")
    @Schema(
            description = "Customer type",
            example = "REGULAR",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = Customer.Type.class
    )
    private Customer.Type type;

    @JsonProperty("first_name")
    @Schema(
            description = "Customer first name",
            example = "John",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String firstName;

    @JsonProperty("last_name")
    @Schema(
            description = "Customer last name",
            example = "Doe",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String lastName;

    @JsonProperty("email")
    @Schema(
            description = "Customer email",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String email;

    @JsonProperty("company_name")
    @Schema(
            description = "Company name",
            example = "Example Corp",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String companyName;

    @JsonProperty("created_at")
    @Schema(
            description = "Creation timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(
            description = "Last update timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant updatedAt;
}
