package de.interzero.oneepr.customer.shopping_cart;

import de.interzero.oneepr.common.AuthUtil;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.CreateShoppingCartItemDto;
import de.interzero.oneepr.customer.shopping_cart.dto.ShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.ShoppingCartMapper;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartDto;
import de.interzero.oneepr.customer.shopping_cart.dto.UpdateShoppingCartItemDto;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static de.interzero.oneepr.common.string.Role.*;

@RestController
@RequestMapping(Api.SHOPPING_CART)
@RequiredArgsConstructor
public class ShoppingCartController {

    private final ShoppingCartService shoppingCartService;

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public ShoppingCartDto create(@RequestBody CreateShoppingCartDto body) {
        ShoppingCart entity = shoppingCartService.create(body);
        return ShoppingCartMapper.toDto(entity);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @GetMapping("/{id}")
    public ShoppingCartDto findOne(@PathVariable String id) {
        return shoppingCartService.findOne(id);
    }


    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/email/{email}")
    public ShoppingCartDto findOneByEmail(@PathVariable String email) {
        ShoppingCart entity = shoppingCartService.findOneByEmail(email, AuthUtil.getRelevantUserDetails());
        return ShoppingCartMapper.toDto(entity);
    }


    @Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER, PARTNER})
    @GetMapping("/purchased/{email}")
    public ShoppingCartDto findLastPurchasedByEmail(@PathVariable String email) {
        ShoppingCart entity = shoppingCartService.findLastPurchasedByEmail(email, AuthUtil.getRelevantUserDetails());
        return ShoppingCartMapper.toDto(entity);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PutMapping("/{id}")
    public ShoppingCartDto update(@PathVariable String id,
                                  @RequestBody UpdateShoppingCartDto body) {
        ShoppingCart entity = shoppingCartService.update(id, body, AuthUtil.getUserDetailsOrNull());
        return ShoppingCartMapper.toDto(entity);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PostMapping("/{id}/items")
    @ResponseStatus(HttpStatus.CREATED)
    public ShoppingCartDto addItem(@PathVariable String id,
                                   @RequestBody CreateShoppingCartItemDto body) {
        ShoppingCart entity = shoppingCartService.addItem(id, body, AuthUtil.getUserDetailsOrNull());
        return ShoppingCartMapper.toDto(entity);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @PutMapping("/{id}/items/{item_id}")
    public ShoppingCartDto updateItem(@PathVariable String id,
                                      @PathVariable("item_id") Integer itemId,
                                      @RequestBody UpdateShoppingCartItemDto body) {
        ShoppingCart entity = shoppingCartService.updateItem(id, itemId, body, AuthUtil.getUserDetailsOrNull());
        return ShoppingCartMapper.toDto(entity);
    }

    // Public endpoint — already permitted in security config (requestMatcher)
    @PreAuthorize("permitAll()")
    @DeleteMapping("/{id}/items/{item_id}")
    public ShoppingCartDto removeItem(@PathVariable String id,
                                      @PathVariable("item_id") Integer itemId) {
        ShoppingCart entity = shoppingCartService.removeItem(id, itemId, AuthUtil.getUserDetailsOrNull());
        return ShoppingCartMapper.toDto(entity);
    }
}
