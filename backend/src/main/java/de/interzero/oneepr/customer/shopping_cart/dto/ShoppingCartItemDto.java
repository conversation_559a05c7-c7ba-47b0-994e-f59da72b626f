package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.third_party_cost.dto.OtherCostResponseDto;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCartItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Schema(description = "Shopping cart item data transfer object")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShoppingCartItemDto extends BaseDto {

    @JsonProperty("id")
    @Schema(
            description = "Shopping cart item ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer id;

    @JsonProperty("country_id")
    @Schema(
            description = "Country ID",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer countryId;

    @JsonProperty("country_code")
    @Schema(
            description = "Country code",
            example = "DE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryCode;

    @JsonProperty("country_name")
    @Schema(
            description = "Country name",
            example = "Germany",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryName;

    @JsonProperty("country_flag")
    @Schema(
            description = "Country flag",
            example = "🇩🇪",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryFlag;

    @JsonProperty("year")
    @Schema(
            description = "License year",
            example = "2024",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer year;

    @JsonProperty("price_list")
    @Schema(
            description = "Price list information",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Map<String, Object> priceList;

    @JsonProperty("third_party_costs")
    @Schema(
            description = "Third Party Cost By Country",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<OtherCostResponseDto> thirdPartyCostDtos;

    @JsonProperty("packaging_services")
    @Schema(
            description = "Packaging services",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<PackagingServiceDto> packagingServices;

    @JsonProperty("created_at")
    @Schema(
            description = "Creation timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(
            description = "Last update timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant updatedAt;

    @JsonProperty("calculator")
    @Schema(
            description = "Calculator data",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Map<String, Object> calculator;

    @JsonProperty("price")
    @Schema(
            description = "Item price in cents",
            example = "1000",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Integer price;

    @JsonProperty("service_type")
    @Schema(
            description = "Service type",
            example = "EU_LICENSE",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = Contract.Type.class
    )
    private Contract.Type serviceType;

    @JsonProperty("specification_type")
    @Schema(
            description = "Specification type",
            example = "PURCHASE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            implementation = ShoppingCartItem.SpecificationType.class
    )
    private ShoppingCartItem.SpecificationType specificationType;
}
