package de.interzero.oneepr.customer.shopping_cart.dto;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCartItem;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility class for mapping between ShoppingCart entities and DTOs
 */
public class ShoppingCartMapper {

    private ShoppingCartMapper() {
        // Utility class - prevent instantiation
    }

    /**
     * Converts a ShoppingCart entity to a ShoppingCartDto
     *
     * @param entity the ShoppingCart entity to convert
     * @return the converted ShoppingCartDto
     */
    public static ShoppingCartDto toDto(ShoppingCart entity) {
        if (entity == null) {
            return null;
        }

        ShoppingCartDto dto = new ShoppingCartDto();
        dto.setId(entity.getId());
        dto.setEmail(entity.getEmail());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setIsChurned(entity.getIsChurned());
        dto.setJourney(entity.getJourney());
        dto.setJourneyStep(entity.getJourneyStep());
        dto.setPayment(entity.getPayment());
        dto.setStatus(entity.getStatus());
        dto.setSubtotal(entity.getSubtotal());
        dto.setTotal(entity.getTotal());
        dto.setVatPercentage(entity.getVatPercentage());
        dto.setVatValue(entity.getVatValue());
        dto.setCouponValue(entity.getCouponValue());
        dto.setAffiliateLink(entity.getAffiliateLink());
        dto.setAffiliateType(entity.getAffiliateType());

        // Map nested objects
        dto.setCustomer(toCustomerDto(entity.getCustomer()));
        dto.setItems(toShoppingCartItemDtoList(entity.getItems()));
        dto.setCustomerCommitments(toCustomerCommitmentDtoList(entity.getCustomerCommitments()));

        return dto;
    }

    /**
     * Converts a Customer entity to a CustomerDto
     *
     * @param entity the Customer entity to convert
     * @return the converted CustomerDto
     */
    public static CustomerDto toCustomerDto(Customer entity) {
        if (entity == null) {
            return null;
        }

        CustomerDto dto = new CustomerDto();
        dto.setId(entity.getId());
        dto.setType(entity.getType());
        dto.setFirstName(entity.getFirstName());
        dto.setLastName(entity.getLastName());
        dto.setEmail(entity.getEmail());
        dto.setCompanyName(entity.getCompanyName());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());

        return dto;
    }

    /**
     * Converts a ShoppingCartItem entity to a ShoppingCartItemDto
     *
     * @param entity the ShoppingCartItem entity to convert
     * @return the converted ShoppingCartItemDto
     */
    public static ShoppingCartItemDto toShoppingCartItemDto(ShoppingCartItem entity) {
        if (entity == null) {
            return null;
        }

        ShoppingCartItemDto dto = new ShoppingCartItemDto();
        dto.setId(entity.getId());
        dto.setCountryId(entity.getCountryId());
        dto.setCountryCode(entity.getCountryCode());
        dto.setCountryName(entity.getCountryName());
        dto.setCountryFlag(entity.getCountryFlag());
        dto.setYear(entity.getYear());
        dto.setPriceList(entity.getPriceList());
        dto.setPackagingServices(entity.getPackagingServices());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setCalculator(entity.getCalculator());
        dto.setPrice(entity.getPrice());
        dto.setServiceType(entity.getServiceType());
        dto.setSpecificationType(entity.getSpecificationType());

        return dto;
    }

    /**
     * Converts a CustomerCommitment entity to a CustomerCommitmentDto
     *
     * @param entity the CustomerCommitment entity to convert
     * @return the converted CustomerCommitmentDto
     */
    public static CustomerCommitmentDto toCustomerCommitmentDto(CustomerCommitment entity) {
        if (entity == null) {
            return null;
        }

        CustomerCommitmentDto dto = new CustomerCommitmentDto();
        dto.setId(entity.getId());
        dto.setCustomerEmail(entity.getCustomerEmail());
        dto.setCountryCode(entity.getCountryCode());
        dto.setYear(entity.getYear());
        dto.setCommitment(entity.getCommitment());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());

        return dto;
    }

    /**
     * Converts a list of ShoppingCartItem entities to a list of ShoppingCartItemDto
     *
     * @param entities the list of ShoppingCartItem entities to convert
     * @return the converted list of ShoppingCartItemDto
     */
    public static List<ShoppingCartItemDto> toShoppingCartItemDtoList(List<ShoppingCartItem> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(ShoppingCartMapper::toShoppingCartItemDto)
                .collect(Collectors.toList());
    }

    /**
     * Converts a list of CustomerCommitment entities to a list of CustomerCommitmentDto
     *
     * @param entities the list of CustomerCommitment entities to convert
     * @return the converted list of CustomerCommitmentDto
     */
    public static List<CustomerCommitmentDto> toCustomerCommitmentDtoList(List<CustomerCommitment> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(ShoppingCartMapper::toCustomerCommitmentDto)
                .collect(Collectors.toList());
    }
}
