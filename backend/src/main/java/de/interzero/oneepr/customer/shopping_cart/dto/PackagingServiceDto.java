package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Schema(description = "Packaging service")
@Data
public class PackagingServiceDto extends BaseDto {

    @NotNull
    @JsonProperty("id")
    @Schema(
            description = "Service ID",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer id;

    @NotNull
    @JsonProperty("name")
    @Schema(
            description = "Service name",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String name;

    @NotNull
    @JsonProperty("fractions")
    @Schema(
            description = "Fraction mappings",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Map<String, FractionDto> fractions;

    @EqualsAndHashCode(callSuper = true)
    @Schema(description = "Fraction inside packaging service")
    @Data
    public static class FractionDto extends BaseDto {

        @NotNull
        @JsonProperty("code")
        @Schema(
                description = "Fraction code",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        private String code;

        @NotNull
        @JsonProperty("name")
        @Schema(
                description = "Fraction name",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        private String name;

        @NotNull
        @JsonProperty("weight")
        @Schema(
                description = "Fraction weight",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        private Integer weight;
    }
}


