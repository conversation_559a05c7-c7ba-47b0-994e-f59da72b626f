package de.interzero.oneepr.customer.shopping_cart;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import de.interzero.oneepr.customer.partner.Partner;
import de.interzero.oneepr.customer.purchase.CouponUses;
import jakarta.persistence.*;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.*;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Entity
@Table(
        name = "shopping_cart",
        schema = "public"
)
public class ShoppingCart {

    @Id
    @Column(
            name = "id",
            nullable = false,
            updatable = false,
            columnDefinition = "text"
    )
    @JsonProperty("id")
    private String id;

    @Column(name = "cart_json")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonIgnore
    private Map<String, Object> cartJson;

    @Column(
            name = "email",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("email")
    private String email;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonIgnore
    private LocalDate deletedAt;

    @Column(name = "invoice_id")
    @JsonIgnore
    private Integer invoiceId;

    @NotNull
    @Column(
            name = "is_churned",
            nullable = false
    )
    @JsonProperty("is_churned")
    private Boolean isChurned = false;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "journey")
    @JsonProperty("journey")
    private Journey journey;

    @Column(
            name = "journey_step",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("journey_step")
    private String journeyStep;

    @Column(name = "payment")
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("payment")
    private Map<String, Object> payment;

    @ColumnDefault("'OPEN'")
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "status")
    @JsonProperty("status")
    private Status status;

    @NotNull
    @Column(
            name = "subtotal",
            nullable = false
    )
    @JsonProperty("subtotal")
    private Integer subtotal;

    @NotNull
    @Column(
            name = "total",
            nullable = false
    )
    @JsonProperty("total")
    private Integer total;

    @NotNull
    @Column(
            name = "vat_percentage",
            nullable = false
    )
    @JsonProperty("vat_percentage")
    private Integer vatPercentage;

    @NotNull
    @Column(
            name = "vat_value",
            nullable = false
    )
    @JsonProperty("vat_value")
    private Integer vatValue;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "coupon_id")
    @JsonProperty("coupon")
    private Coupon coupon;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "coupon_type")
    @JsonProperty("coupon_type")
    private CouponType couponType;

    @NotNull
    @Column(
            name = "coupon_value",
            nullable = false
    )
    @JsonProperty("coupon_value")
    private Integer couponValue;

    @Column(
            name = "coupon_url_link",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("coupon_url_link")
    private String couponUrlLink;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "affiliate_customer_id")
    @JsonIgnore
    private Customer affiliateCustomer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "email",
            referencedColumnName = "email",
            insertable = false,
            updatable = false
    )
    @JsonProperty("customer")
    private Customer customer;

    @OneToMany(mappedBy = "shoppingCart")
    @SQLRestriction("deleted_at IS NULL")
    @OrderBy("id ASC")
    @JsonProperty("items")
    private List<ShoppingCartItem> items = new ArrayList<>();

    @OneToMany(mappedBy = "shoppingCart")
    @SQLRestriction("deleted_at IS NULL")
    @JsonProperty("customer_commitments")
    @JsonIgnoreProperties("shopping_cart")
    private List<CustomerCommitment> customerCommitments = new ArrayList<>();

    @Column(
            name = "affiliate_link",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("affiliate_link")
    private String affiliateLink;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "affiliate_partner_id")
    @JsonIgnore
    private Partner affiliatePartner;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "affiliate_type")
    @JsonProperty("affiliate_type")
    private TypeAffiliate affiliateType;

    @OneToMany(
            mappedBy = "shoppingCart",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    private List<CouponUses> couponUses = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        if (this.id == null) {
            this.id = java.util.UUID.randomUUID().toString();
        }
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public enum Journey {
        LONG,
        DIRECT_LICENSE,
        QUICK_LICENSE,
        QUICK_ACTION_GUIDE
    }

    public enum Status {
        OPEN,
        PURCHASED
    }

    public enum CouponType {
        LINK,
        WRITTEN
    }

    public enum TypeAffiliate {
        AFFILIATE_LINK,
        COUPON
    }
}