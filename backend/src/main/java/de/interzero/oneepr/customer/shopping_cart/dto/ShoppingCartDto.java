package de.interzero.oneepr.customer.shopping_cart.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.shopping_cart.ShoppingCart;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Schema(description = "Shopping cart data transfer object")
@Data
@EqualsAndHashCode(callSuper = true)
public class ShoppingCartDto extends BaseDto {

    @JsonProperty("id")
    @Schema(
            description = "Shopping cart ID",
            example = "550e8400-e29b-41d4-a716-446655440000",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String id;

    @JsonProperty("email")
    @Schema(
            description = "Customer email",
            example = "<EMAIL>",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String email;

    @JsonProperty("created_at")
    @Schema(
            description = "Creation timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant createdAt;

    @JsonProperty("updated_at")
    @Schema(
            description = "Last update timestamp",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Instant updatedAt;

    @JsonProperty("is_churned")
    @Schema(
            description = "Whether the cart is churned",
            example = "false",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Boolean isChurned;

    @JsonProperty("journey")
    @Schema(
            description = "Shopping cart journey type",
            example = "DIRECT_LICENSE",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ShoppingCart.Journey.class
    )
    private ShoppingCart.Journey journey;

    @JsonProperty("journey_step")
    @Schema(
            description = "Current journey step",
            example = "SHOPPING_CART",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String journeyStep;

    @JsonProperty("payment")
    @Schema(
            description = "Payment information",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private Map<String, Object> payment;

    @JsonProperty("status")
    @Schema(
            description = "Shopping cart status",
            example = "OPEN",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ShoppingCart.Status.class
    )
    private ShoppingCart.Status status;

    @JsonProperty("subtotal")
    @Schema(
            description = "Subtotal amount in cents",
            example = "1000",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer subtotal;

    @JsonProperty("total")
    @Schema(
            description = "Total amount in cents",
            example = "1190",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer total;

    @JsonProperty("vat_percentage")
    @Schema(
            description = "VAT percentage",
            example = "19",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer vatPercentage;

    @JsonProperty("vat_value")
    @Schema(
            description = "VAT amount in cents",
            example = "190",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer vatValue;

    @JsonProperty("coupon_value")
    @Schema(
            description = "Coupon discount value in cents",
            example = "100",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer couponValue;

    @JsonProperty("customer")
    @Schema(
            description = "Customer information",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private CustomerDto customer;

    @JsonProperty("items")
    @Schema(
            description = "Shopping cart items",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<ShoppingCartItemDto> items;

    @JsonProperty("customer_commitments")
    @Schema(
            description = "Customer commitments",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private List<CustomerCommitmentDto> customerCommitments;

    @JsonProperty("affiliate_link")
    @Schema(
            description = "Affiliate link",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    private String affiliateLink;

    @JsonProperty("affiliate_type")
    @Schema(
            description = "Affiliate type",
            example = "AFFILIATE_LINK",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED,
            implementation = ShoppingCart.TypeAffiliate.class
    )
    private ShoppingCart.TypeAffiliate affiliateType;
}
