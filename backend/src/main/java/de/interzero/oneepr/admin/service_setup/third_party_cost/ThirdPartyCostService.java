package de.interzero.oneepr.admin.service_setup.third_party_cost;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.third_party_cost.dto.CreateOtherCostDto;
import de.interzero.oneepr.admin.service_setup.third_party_cost.dto.UpdateOtherCostDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service for managing other costs.
 */
@Service
@RequiredArgsConstructor
public class ThirdPartyCostService {

    private final ThirdPartyCostRepository thirdPartyCostRepository;

    private final CountryRepository countryRepository;

    private static final String OTHER_COST_NOT_FOUND = "Other cost not found";

    /**
     * Creates a new OtherCost record.
     *
     * @param data The DTO containing creation data.
     * @return The newly created OtherCost entity.
     */
    @Transactional
    public ThirdPartyCost create(CreateOtherCostDto data) {
        Country country = countryRepository.findById(data.getCountryId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));

        if (data.getPrice() < 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Price must be greater than 0");
        }

        ThirdPartyCost thirdPartyCost = new ThirdPartyCost();
        thirdPartyCost.setName(data.getName());
        thirdPartyCost.setPrice(data.getPrice());
        thirdPartyCost.setCountry(country);

        return thirdPartyCostRepository.save(thirdPartyCost);
    }

    /**
     * Finds all non-deleted other costs.
     *
     * @return A list of all active other costs.
     */
    @Transactional(readOnly = true)
    public List<ThirdPartyCost> findAll() {
        return thirdPartyCostRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds a single non-deleted other cost by its ID.
     *
     * @param id The ID of the other cost to find.
     * @return The found OtherCost entity.
     */
    @Transactional(readOnly = true)
    public ThirdPartyCost findOne(Integer id) {
        return thirdPartyCostRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));
    }

    /**
     * Partially updates an existing other cost record.
     *
     * @param id   The ID of the other cost to update.
     * @param data DTO containing the fields to update.
     * @return The updated OtherCost entity.
     */
    @Transactional
    public ThirdPartyCost update(Integer id,
                                 UpdateOtherCostDto data) {
        ThirdPartyCost thirdPartyCost = thirdPartyCostRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(data, thirdPartyCost);

        return thirdPartyCostRepository.save(thirdPartyCost);
    }

    /**
     * Soft-deletes an other cost by setting its deleted_at timestamp.
     *
     * @param id The ID of the other cost to remove.
     * @return The updated entity with the `deletedAt` timestamp set.
     */
    @Transactional
    public ThirdPartyCost remove(Integer id) {
        ThirdPartyCost thirdPartyCost = thirdPartyCostRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, OTHER_COST_NOT_FOUND));

        thirdPartyCost.setDeletedAt(Instant.now());
        return thirdPartyCostRepository.save(thirdPartyCost);
    }
}
