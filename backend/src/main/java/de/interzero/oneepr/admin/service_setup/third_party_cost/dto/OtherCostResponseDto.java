package de.interzero.oneepr.admin.service_setup.third_party_cost.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.dto.CountryWithCriteriaDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * Data Transfer Object for the response of the findServiceSetupOtherCosts method.
 * It represents an OtherCost entity, augmented with the associated country
 * (which contains filtered criteria) and a flag indicating if any such criteria exist.
 * This structure mirrors the transformation performed in the original NestJS service.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class OtherCostResponseDto {

    @Schema(description = "The unique identifier of the other cost.", example = "1")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The name of the other cost.", example = "Handling Fee")
    @JsonProperty("name")
    private String name;

    @Schema(description = "The price of the other cost.", example = "50")
    @JsonProperty("price")
    private Integer price;

    @Schema(description = "ID of the associated country.", example = "1")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The associated country, including its filtered criteria.")
    @JsonProperty("country")
    private CountryWithCriteriaDto country;

    @Schema(description = "Flag indicating if the associated country has commitment criteria for other costs.", example = "true")
    @JsonProperty("has_criteria")
    private boolean hasCriteria;
}