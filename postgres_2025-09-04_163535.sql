--
-- PostgreSQL database dump
--

-- Dumped from database version 17.3 (Debian 17.3-3.pgdg120+1)
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: pg_database_owner
--

CREATE SCHEMA public;


ALTER SCHEMA public OWNER TO pg_database_owner;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: pg_database_owner
--

COMMENT ON SCHEMA public IS 'standard public schema';


--
-- Name: BrokerCompanyOrderStatus; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."BrokerCompanyOrderStatus" AS ENUM (
    'OPEN',
    'CANCELLED'
);


ALTER TYPE public."BrokerCompanyOrderStatus" OWNER TO "oneepr-local-user";

--
-- Name: BrokerCompanyOrderType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."BrokerCompanyOrderType" AS ENUM (
    'INITIAL_REPORT',
    'REPORT'
);


ALTER TYPE public."BrokerCompanyOrderType" OWNER TO "oneepr-local-user";

--
-- Name: ClusterStatus; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."ClusterStatus" AS ENUM (
    'ACTIVE',
    'INACTIVE'
);


ALTER TYPE public."ClusterStatus" OWNER TO "oneepr-local-user";

--
-- Name: CouponDiscountType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."CouponDiscountType" AS ENUM (
    'PERCENTAGE',
    'ABSOLUTE',
    'BUY_X_PRODUCTS_GET_Y_PRODUCTS',
    'BUY_X_PRODUCTS_GET_Y_DISCOUNT'
);


ALTER TYPE public."CouponDiscountType" OWNER TO "oneepr-local-user";

--
-- Name: CouponMode; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."CouponMode" AS ENUM (
    'GENERAL',
    'INDIVIDUAL',
    'GROUP_SEGMENT'
);


ALTER TYPE public."CouponMode" OWNER TO "oneepr-local-user";

--
-- Name: CouponType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."CouponType" AS ENUM (
    'SYSTEM',
    'CUSTOMER'
);


ALTER TYPE public."CouponType" OWNER TO "oneepr-local-user";

--
-- Name: DeclineReasonType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."DeclineReasonType" AS ENUM (
    'LICENSE_INFORMATION',
    'LICENSE_VOLUME_REPORT'
);


ALTER TYPE public."DeclineReasonType" OWNER TO "oneepr-local-user";

--
-- Name: LicenseThirdPartyInvoiceIssuer; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."LicenseThirdPartyInvoiceIssuer" AS ENUM (
    'THIRD_PARTY_DUAL_SYSTEM',
    'OTHER_THIRD_PARTY'
);


ALTER TYPE public."LicenseThirdPartyInvoiceIssuer" OWNER TO "oneepr-local-user";

--
-- Name: PartnerContractChangeType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."PartnerContractChangeType" AS ENUM (
    'EMAIL',
    'NOTE',
    'FILE'
);


ALTER TYPE public."PartnerContractChangeType" OWNER TO "oneepr-local-user";

--
-- Name: PartnerContractStatus; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."PartnerContractStatus" AS ENUM (
    'DRAFT',
    'ACTIVE',
    'EXPIRED',
    'TERMINATED',
    'TO_BE_SIGNED',
    'DENIED'
);


ALTER TYPE public."PartnerContractStatus" OWNER TO "oneepr-local-user";

--
-- Name: PartnerStatus; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."PartnerStatus" AS ENUM (
    'NO_UPDATES',
    'IMPROVED_CONTRACT',
    'DENIED_CONTRACT',
    'REQUESTED_COMMISSION',
    'CHANGED_INFORMATION'
);


ALTER TYPE public."PartnerStatus" OWNER TO "oneepr-local-user";

--
-- Name: PriceListConditionType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."PriceListConditionType" AS ENUM (
    'LICENSE_YEAR'
);


ALTER TYPE public."PriceListConditionType" OWNER TO "oneepr-local-user";

--
-- Name: ReportSetColumnUnitType; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."ReportSetColumnUnitType" AS ENUM (
    'KG',
    'UNITS',
    'EACH'
);


ALTER TYPE public."ReportSetColumnUnitType" OWNER TO "oneepr-local-user";

--
-- Name: Services; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."Services" AS ENUM (
    'CUSTOMER',
    'AUTH',
    'CALCULATOR',
    'CLERK',
    'PAYMENT',
    'SHOP',
    'SUBSCRIPTION',
    'FRONT_END'
);


ALTER TYPE public."Services" OWNER TO "oneepr-local-user";

--
-- Name: Status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."Status" AS ENUM (
    'NOT_VERIFIED',
    'VERIFIED_EMAIL',
    'LOGIN',
    'PENDING_PASSWORD',
    'COMPLETE'
);


ALTER TYPE public."Status" OWNER TO "oneepr-local-user";

--
-- Name: TypeNotifications; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public."TypeNotifications" AS ENUM (
    'EMAIL',
    'SMS'
);


ALTER TYPE public."TypeNotifications" OWNER TO "oneepr-local-user";

--
-- Name: criteria_calculator_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.criteria_calculator_type AS ENUM (
    'LICENSE_FEES',
    'TOTAL_IN_TONS',
    'TOTAL_IN_KG'
);


ALTER TYPE public.criteria_calculator_type OWNER TO "oneepr-local-user";

--
-- Name: criteria_input_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.criteria_input_type AS ENUM (
    'RADIO',
    'SELECT',
    'YES_NO',
    'RANGE'
);


ALTER TYPE public.criteria_input_type OWNER TO "oneepr-local-user";

--
-- Name: criteria_mode; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.criteria_mode AS ENUM (
    'COMMITMENT',
    'CALCULATOR'
);


ALTER TYPE public.criteria_mode OWNER TO "oneepr-local-user";

--
-- Name: criteria_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.criteria_type AS ENUM (
    'PACKAGING_SERVICE',
    'REPORT_SET',
    'REPORT_FREQUENCY',
    'AUTHORIZE_REPRESENTATIVE',
    'REPRESENTATIVE_TIER',
    'OTHER_COST',
    'PRICE_LIST',
    'REQUIRED_INFORMATION'
);


ALTER TYPE public.criteria_type OWNER TO "oneepr-local-user";

--
-- Name: customer_activity_type_enum; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.customer_activity_type_enum AS ENUM (
    'ACCOUNT_LOGIN',
    'ACCOUNT_UPDATE_PASSWORD',
    'ACCOUNT_UPDATE_EMAIL',
    'ACCOUNT_UPDATE_ADDRESS',
    'ACCOUNT_UPDATE_PAYMENT',
    'CONTRACT_ADD_SERVICE',
    'CONTRACT_UPDATE_SERVICE',
    'CONTRACT_TERMINATION',
    'CONTRACT_SERVICE_TERMINATION',
    'REPORT_ADD',
    'REPORT_UPDATE',
    'DOCUMENT_UPLOAD',
    'DOCUMENT_ANSWER',
    'DOCUMENT_DOWNLOAD'
);


ALTER TYPE public.customer_activity_type_enum OWNER TO "oneepr-local-user";

--
-- Name: enum_certificate_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_certificate_status AS ENUM (
    'AVAILABLE',
    'NOT_AVAILABLE'
);


ALTER TYPE public.enum_certificate_status OWNER TO "oneepr-local-user";

--
-- Name: enum_commission_service_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_commission_service_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
);


ALTER TYPE public.enum_commission_service_type OWNER TO "oneepr-local-user";

--
-- Name: enum_commission_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_commission_type AS ENUM (
    'AFFILIATE_LINK',
    'COUPON'
);


ALTER TYPE public.enum_commission_type OWNER TO "oneepr-local-user";

--
-- Name: enum_commission_user_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_commission_user_type AS ENUM (
    'CUSTOMER',
    'PARTNER'
);


ALTER TYPE public.enum_commission_user_type OWNER TO "oneepr-local-user";

--
-- Name: enum_consent_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_consent_type AS ENUM (
    'ACCOUNT',
    'PURCHASE'
);


ALTER TYPE public.enum_consent_type OWNER TO "oneepr-local-user";

--
-- Name: enum_contract_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_contract_status AS ENUM (
    'ACTIVE',
    'TERMINATION_PROCESS',
    'TERMINATED'
);


ALTER TYPE public.enum_contract_status OWNER TO "oneepr-local-user";

--
-- Name: enum_contract_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_contract_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
);


ALTER TYPE public.enum_contract_type OWNER TO "oneepr-local-user";

--
-- Name: enum_customer_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_customer_type AS ENUM (
    'REGULAR',
    'PREMIUM'
);


ALTER TYPE public.enum_customer_type OWNER TO "oneepr-local-user";

--
-- Name: enum_file_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_file_type AS ENUM (
    'GENERAL_INFORMATION',
    'REQUIRED_INFORMATION',
    'CONTRACT',
    'CONTRACT_TERMINATION',
    'LICENSE_CONTRACT',
    'CERTIFICATE',
    'INVOICE',
    'PAYMENT',
    'LICENSE_PROOF_OF_REGISTRATION',
    'PROOF_OF_TERMINATION',
    'THIRD_PARTY_INVOICE',
    'MARKETING_MATERIAL',
    'PARTNER_CONTRACT'
);


ALTER TYPE public.enum_file_type OWNER TO "oneepr-local-user";

--
-- Name: enum_general_information_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_general_information_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);


ALTER TYPE public.enum_general_information_status OWNER TO "oneepr-local-user";

--
-- Name: enum_general_information_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_general_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
);


ALTER TYPE public.enum_general_information_type OWNER TO "oneepr-local-user";

--
-- Name: enum_license_clerk_control_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_clerk_control_status AS ENUM (
    'PENDING',
    'DONE'
);


ALTER TYPE public.enum_license_clerk_control_status OWNER TO "oneepr-local-user";

--
-- Name: enum_license_contract_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_contract_status AS ENUM (
    'ACTIVE',
    'TERMINATION_PROCESS',
    'TERMINATED'
);


ALTER TYPE public.enum_license_contract_status OWNER TO "oneepr-local-user";

--
-- Name: enum_license_registration_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_registration_status AS ENUM (
    'PENDING',
    'IN_REVIEW',
    'REGISTRATION',
    'DONE'
);


ALTER TYPE public.enum_license_registration_status OWNER TO "oneepr-local-user";

--
-- Name: enum_license_report_set_rhythm; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_report_set_rhythm AS ENUM (
    'ANNUALLY',
    'MONTHLY',
    'QUARTERLY'
);


ALTER TYPE public.enum_license_report_set_rhythm OWNER TO "oneepr-local-user";

--
-- Name: enum_license_required_information_kind; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_required_information_kind AS ENUM (
    'REQUIRED_INFORMATION',
    'GENERAL_INFORMATION'
);


ALTER TYPE public.enum_license_required_information_kind OWNER TO "oneepr-local-user";

--
-- Name: enum_license_required_information_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_required_information_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);


ALTER TYPE public.enum_license_required_information_status OWNER TO "oneepr-local-user";

--
-- Name: enum_license_required_information_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_required_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
);


ALTER TYPE public.enum_license_required_information_type OWNER TO "oneepr-local-user";

--
-- Name: enum_license_third_party_invoice_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_third_party_invoice_status AS ENUM (
    'OPEN',
    'PAYED',
    'UNPROCESSED',
    'CANCELLED'
);


ALTER TYPE public.enum_license_third_party_invoice_status OWNER TO "oneepr-local-user";

--
-- Name: enum_license_volume_report_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_license_volume_report_status AS ENUM (
    'NEW',
    'DONE',
    'DECLINED',
    'APPROVED',
    'OPEN'
);


ALTER TYPE public.enum_license_volume_report_status OWNER TO "oneepr-local-user";

--
-- Name: enum_marketing_material_category; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_marketing_material_category AS ENUM (
    'STANDARD',
    'SPECIFIC_MATERIAL'
);


ALTER TYPE public.enum_marketing_material_category OWNER TO "oneepr-local-user";

--
-- Name: enum_marketing_material_partner_restriction; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_marketing_material_partner_restriction AS ENUM (
    'ALL',
    'CLUSTER',
    'SPECIFIC'
);


ALTER TYPE public.enum_marketing_material_partner_restriction OWNER TO "oneepr-local-user";

--
-- Name: enum_reason_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_reason_type AS ENUM (
    'LICENSE_INFORMATION',
    'LICENSE_VOLUME_REPORT',
    'TERMINATION'
);


ALTER TYPE public.enum_reason_type OWNER TO "oneepr-local-user";

--
-- Name: enum_termination_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.enum_termination_status AS ENUM (
    'REQUESTED',
    'COMPLETED',
    'PENDING'
);


ALTER TYPE public.enum_termination_status OWNER TO "oneepr-local-user";

--
-- Name: price_list_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.price_list_type AS ENUM (
    'EU_LICENSE',
    'DIRECT_LICENSE',
    'ACTION_GUIDE'
);


ALTER TYPE public.price_list_type OWNER TO "oneepr-local-user";

--
-- Name: report_set_mode; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.report_set_mode AS ENUM (
    'ON_PLATAFORM',
    'BY_EXCEL',
    'FLAT_RATES'
);


ALTER TYPE public.report_set_mode OWNER TO "oneepr-local-user";

--
-- Name: report_set_price_list_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.report_set_price_list_type AS ENUM (
    'FIXED_PRICE',
    'PRICE_PER_CATEGORY',
    'PRICE_PER_VOLUME_BASE_PRICE',
    'PRICE_PER_VOLUME_MINIMUM_FEE'
);


ALTER TYPE public.report_set_price_list_type OWNER TO "oneepr-local-user";

--
-- Name: report_set_rhythm; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.report_set_rhythm AS ENUM (
    'ANNUALLY',
    'MONTHLY',
    'QUARTERLY'
);


ALTER TYPE public.report_set_rhythm OWNER TO "oneepr-local-user";

--
-- Name: report_set_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.report_set_type AS ENUM (
    'FRACTIONS',
    'CATEGORIES'
);


ALTER TYPE public.report_set_type OWNER TO "oneepr-local-user";

--
-- Name: required_information_kind; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.required_information_kind AS ENUM (
    'COUNTRY_INFORMATION',
    'GENERAL_INFORMATION'
);


ALTER TYPE public.required_information_kind OWNER TO "oneepr-local-user";

--
-- Name: required_information_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.required_information_type AS ENUM (
    'TEXT',
    'NUMBER',
    'DOCUMENT',
    'FILE',
    'IMAGE'
);


ALTER TYPE public.required_information_type OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_item_specification_type; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.shopping_cart_item_specification_type AS ENUM (
    'PURCHASE',
    'VOLUME_CHANGE'
);


ALTER TYPE public.shopping_cart_item_specification_type OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_journey; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.shopping_cart_journey AS ENUM (
    'LONG',
    'DIRECT_LICENSE',
    'QUICK_LICENSE',
    'QUICK_ACTION_GUIDE'
);


ALTER TYPE public.shopping_cart_journey OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_status; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.shopping_cart_status AS ENUM (
    'OPEN',
    'PURCHASED'
);


ALTER TYPE public.shopping_cart_status OWNER TO "oneepr-local-user";

--
-- Name: type_affiliate; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.type_affiliate AS ENUM (
    'AFFILIATE_LINK',
    'COUPON'
);


ALTER TYPE public.type_affiliate OWNER TO "oneepr-local-user";

--
-- Name: type_use_coupon; Type: TYPE; Schema: public; Owner: oneepr-local-user
--

CREATE TYPE public.type_use_coupon AS ENUM (
    'LINK',
    'WRITTEN'
);


ALTER TYPE public.type_use_coupon OWNER TO "oneepr-local-user";

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO "oneepr-local-user";

--
-- Name: action_guide; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.action_guide (
    id integer NOT NULL,
    contract_id integer NOT NULL,
    country_id integer NOT NULL,
    country_code text NOT NULL,
    country_name text NOT NULL,
    country_flag text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    contract_status public.enum_contract_status DEFAULT 'ACTIVE'::public.enum_contract_status NOT NULL,
    termination_id integer
);


ALTER TABLE public.action_guide OWNER TO "oneepr-local-user";

--
-- Name: action_guide_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.action_guide_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.action_guide_id_seq OWNER TO "oneepr-local-user";

--
-- Name: action_guide_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.action_guide_id_seq OWNED BY public.action_guide.id;


--
-- Name: action_guide_price_list; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.action_guide_price_list (
    id integer NOT NULL,
    setup_price_list_id integer NOT NULL,
    action_guide_id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    price integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.action_guide_price_list OWNER TO "oneepr-local-user";

--
-- Name: action_guide_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.action_guide_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.action_guide_price_list_id_seq OWNER TO "oneepr-local-user";

--
-- Name: action_guide_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.action_guide_price_list_id_seq OWNED BY public.action_guide_price_list.id;


--
-- Name: broker; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.broker (
    id integer NOT NULL,
    name text NOT NULL,
    email text NOT NULL,
    phone text NOT NULL,
    enroled_at timestamp(3) without time zone NOT NULL,
    company_name text NOT NULL,
    vat text,
    tax text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone,
    deleted_at timestamp(3) without time zone,
    is_active boolean DEFAULT true NOT NULL,
    user_id integer NOT NULL
);


ALTER TABLE public.broker OWNER TO "oneepr-local-user";

--
-- Name: broker_company; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.broker_company (
    id integer NOT NULL,
    broker_id integer NOT NULL,
    name text NOT NULL,
    register_number text NOT NULL,
    vat text,
    tax text,
    country_code text,
    address_number text,
    address_street text NOT NULL,
    city text NOT NULL,
    contact_name text NOT NULL,
    contact_email text NOT NULL,
    phone_number text NOT NULL,
    file_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.broker_company OWNER TO "oneepr-local-user";

--
-- Name: broker_company_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.broker_company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.broker_company_id_seq OWNER TO "oneepr-local-user";

--
-- Name: broker_company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.broker_company_id_seq OWNED BY public.broker_company.id;


--
-- Name: broker_company_order; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.broker_company_order (
    id integer NOT NULL,
    customer_number text NOT NULL,
    company_id integer NOT NULL,
    transfer_date timestamp(3) without time zone NOT NULL,
    order_number text NOT NULL,
    year integer NOT NULL,
    fractions jsonb[],
    status public."BrokerCompanyOrderStatus" NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp(3) without time zone,
    updated_at timestamp(3) without time zone,
    type public."BrokerCompanyOrderType" NOT NULL,
    net_value integer NOT NULL,
    file_id integer NOT NULL
);


ALTER TABLE public.broker_company_order OWNER TO "oneepr-local-user";

--
-- Name: broker_company_order_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.broker_company_order_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.broker_company_order_id_seq OWNER TO "oneepr-local-user";

--
-- Name: broker_company_order_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.broker_company_order_id_seq OWNED BY public.broker_company_order.id;


--
-- Name: broker_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.broker_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.broker_id_seq OWNER TO "oneepr-local-user";

--
-- Name: broker_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.broker_id_seq OWNED BY public.broker.id;


--
-- Name: certificate; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.certificate (
    id integer NOT NULL,
    license_id integer NOT NULL,
    name text NOT NULL,
    status public.enum_certificate_status DEFAULT 'NOT_AVAILABLE'::public.enum_certificate_status NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.certificate OWNER TO "oneepr-local-user";

--
-- Name: certificate_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.certificate_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.certificate_id_seq OWNER TO "oneepr-local-user";

--
-- Name: certificate_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.certificate_id_seq OWNED BY public.certificate.id;


--
-- Name: change_user_email; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.change_user_email (
    id uuid NOT NULL,
    new_email text NOT NULL,
    token text NOT NULL,
    user_id integer NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.change_user_email OWNER TO "oneepr-local-user";

--
-- Name: change_user_email_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.change_user_email_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.change_user_email_id_seq OWNER TO "oneepr-local-user";

--
-- Name: change_user_email_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.change_user_email_id_seq OWNED BY public.change_user_email.id;


--
-- Name: cluster; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.cluster (
    id integer NOT NULL,
    name text NOT NULL,
    registration_start_date timestamp(3) without time zone NOT NULL,
    registration_end_date timestamp(3) without time zone NOT NULL,
    status public."ClusterStatus" NOT NULL,
    min_household_packaging integer NOT NULL,
    max_household_packaging integer NOT NULL,
    type_of_services jsonb NOT NULL,
    participating_countries jsonb NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.cluster OWNER TO "oneepr-local-user";

--
-- Name: cluster_customers; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.cluster_customers (
    id integer NOT NULL,
    cluster_id integer NOT NULL,
    customer_id integer NOT NULL
);


ALTER TABLE public.cluster_customers OWNER TO "oneepr-local-user";

--
-- Name: cluster_customers_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.cluster_customers_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cluster_customers_id_seq OWNER TO "oneepr-local-user";

--
-- Name: cluster_customers_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.cluster_customers_id_seq OWNED BY public.cluster_customers.id;


--
-- Name: cluster_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.cluster_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cluster_id_seq OWNER TO "oneepr-local-user";

--
-- Name: cluster_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.cluster_id_seq OWNED BY public.cluster.id;


--
-- Name: commission; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.commission (
    id integer NOT NULL,
    user_id integer NOT NULL,
    user_type public.enum_commission_user_type NOT NULL,
    commission_percentage integer NOT NULL,
    commission_value integer NOT NULL,
    net_turnover integer DEFAULT 0 NOT NULL,
    type public.enum_commission_type NOT NULL,
    coupon_id integer,
    coupon_code text,
    affiliate_link text,
    service_type public.enum_commission_service_type NOT NULL,
    order_id integer NOT NULL,
    order_customer_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    used boolean DEFAULT false NOT NULL
);


ALTER TABLE public.commission OWNER TO "oneepr-local-user";

--
-- Name: commission_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.commission_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.commission_id_seq OWNER TO "oneepr-local-user";

--
-- Name: commission_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.commission_id_seq OWNED BY public.commission.id;


--
-- Name: company; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.company (
    id integer NOT NULL,
    name text NOT NULL,
    description text,
    vat text,
    tin text,
    lucid text,
    customer_id integer,
    starting timestamp(3) without time zone,
    website text,
    partner_id integer,
    address_id integer,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    industry_sector text,
    owner_name text
);


ALTER TABLE public.company OWNER TO "oneepr-local-user";

--
-- Name: company_address; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.company_address (
    id integer NOT NULL,
    country_code text NOT NULL,
    address_line text NOT NULL,
    city text NOT NULL,
    zip_code text NOT NULL,
    street_and_number text NOT NULL,
    additional_address text NOT NULL,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.company_address OWNER TO "oneepr-local-user";

--
-- Name: company_address_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.company_address_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_address_id_seq OWNER TO "oneepr-local-user";

--
-- Name: company_address_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.company_address_id_seq OWNED BY public.company_address.id;


--
-- Name: company_billing; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.company_billing (
    id integer NOT NULL,
    company_id integer,
    is_custom boolean DEFAULT false NOT NULL,
    full_name text,
    country_code text,
    country_name text,
    company_name text,
    street_and_number text,
    city text,
    zip_code text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.company_billing OWNER TO "oneepr-local-user";

--
-- Name: company_billing_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.company_billing_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_billing_id_seq OWNER TO "oneepr-local-user";

--
-- Name: company_billing_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.company_billing_id_seq OWNED BY public.company_billing.id;


--
-- Name: company_contact; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.company_contact (
    id integer NOT NULL,
    company_id integer,
    name text,
    email text,
    phone_mobile text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.company_contact OWNER TO "oneepr-local-user";

--
-- Name: company_contact_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.company_contact_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_contact_id_seq OWNER TO "oneepr-local-user";

--
-- Name: company_contact_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.company_contact_id_seq OWNED BY public.company_contact.id;


--
-- Name: company_email; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.company_email (
    id integer NOT NULL,
    email text NOT NULL,
    company_id integer,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.company_email OWNER TO "oneepr-local-user";

--
-- Name: company_email_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.company_email_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_email_id_seq OWNER TO "oneepr-local-user";

--
-- Name: company_email_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.company_email_id_seq OWNED BY public.company_email.id;


--
-- Name: company_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.company_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.company_id_seq OWNER TO "oneepr-local-user";

--
-- Name: company_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.company_id_seq OWNED BY public.company.id;


--
-- Name: consent; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.consent (
    id integer NOT NULL,
    name text NOT NULL,
    type public.enum_consent_type NOT NULL,
    description text NOT NULL,
    version integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.consent OWNER TO "oneepr-local-user";

--
-- Name: consent_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.consent_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.consent_id_seq OWNER TO "oneepr-local-user";

--
-- Name: consent_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.consent_id_seq OWNED BY public.consent.id;


--
-- Name: contract; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.contract (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    type public.enum_contract_type NOT NULL,
    status public.enum_contract_status DEFAULT 'ACTIVE'::public.enum_contract_status NOT NULL,
    title text NOT NULL,
    start_date timestamp(3) without time zone NOT NULL,
    termination_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    end_date timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.contract OWNER TO "oneepr-local-user";

--
-- Name: contract_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.contract_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.contract_id_seq OWNER TO "oneepr-local-user";

--
-- Name: contract_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.contract_id_seq OWNED BY public.contract.id;


--
-- Name: country; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.country (
    id integer NOT NULL,
    name text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone,
    authorize_representative_obligated boolean DEFAULT false NOT NULL,
    code text NOT NULL,
    flag_url text NOT NULL,
    other_costs_obligated boolean DEFAULT false NOT NULL,
    is_published boolean DEFAULT false NOT NULL,
    license_required boolean DEFAULT false
);


ALTER TABLE public.country OWNER TO "oneepr-local-user";

--
-- Name: country_follower; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.country_follower (
    id integer NOT NULL,
    country_id integer NOT NULL,
    user_id integer NOT NULL,
    user_email text NOT NULL,
    user_first_name text NOT NULL,
    user_last_name text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.country_follower OWNER TO "oneepr-local-user";

--
-- Name: country_follower_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.country_follower_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_follower_id_seq OWNER TO "oneepr-local-user";

--
-- Name: country_follower_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.country_follower_id_seq OWNED BY public.country_follower.id;


--
-- Name: country_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_id_seq OWNER TO "oneepr-local-user";

--
-- Name: country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.country_id_seq OWNED BY public.country.id;


--
-- Name: country_price_list; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.country_price_list (
    id integer NOT NULL,
    country_id integer NOT NULL,
    price_list_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.country_price_list OWNER TO "oneepr-local-user";

--
-- Name: country_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.country_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.country_price_list_id_seq OWNER TO "oneepr-local-user";

--
-- Name: country_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.country_price_list_id_seq OWNED BY public.country_price_list.id;


--
-- Name: coupon; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.coupon (
    id integer NOT NULL,
    buy_x_get_y jsonb,
    code text NOT NULL,
    commission_percentage integer,
    description text,
    discount_type public."CouponDiscountType" NOT NULL,
    elegible_products jsonb,
    end_date timestamp(3) without time zone NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    link text,
    max_amount integer,
    max_uses integer,
    max_uses_per_customer integer,
    min_amount integer,
    min_products integer,
    mode public."CouponMode" NOT NULL,
    note text,
    start_date timestamp(3) without time zone NOT NULL,
    type public."CouponType" NOT NULL,
    value integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    redeemable_by_new_customers boolean DEFAULT false NOT NULL,
    for_commission boolean DEFAULT false NOT NULL,
    used_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.coupon OWNER TO "oneepr-local-user";

--
-- Name: coupon_customer; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.coupon_customer (
    id integer NOT NULL,
    coupon_id integer NOT NULL,
    customer_id integer NOT NULL
);


ALTER TABLE public.coupon_customer OWNER TO "oneepr-local-user";

--
-- Name: coupon_customer_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.coupon_customer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.coupon_customer_id_seq OWNER TO "oneepr-local-user";

--
-- Name: coupon_customer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.coupon_customer_id_seq OWNED BY public.coupon_customer.id;


--
-- Name: coupon_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.coupon_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.coupon_id_seq OWNER TO "oneepr-local-user";

--
-- Name: coupon_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.coupon_id_seq OWNED BY public.coupon.id;


--
-- Name: coupon_partners; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.coupon_partners (
    id integer NOT NULL,
    coupon_id integer NOT NULL,
    partner_id integer NOT NULL
);


ALTER TABLE public.coupon_partners OWNER TO "oneepr-local-user";

--
-- Name: coupon_partners_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.coupon_partners_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.coupon_partners_id_seq OWNER TO "oneepr-local-user";

--
-- Name: coupon_partners_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.coupon_partners_id_seq OWNED BY public.coupon_partners.id;


--
-- Name: coupon_uses; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.coupon_uses (
    id integer NOT NULL,
    coupon_id integer NOT NULL,
    shopping_cart_id text NOT NULL,
    order_id integer,
    customer_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    is_first_purchase boolean DEFAULT false NOT NULL
);


ALTER TABLE public.coupon_uses OWNER TO "oneepr-local-user";

--
-- Name: coupon_uses_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.coupon_uses_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.coupon_uses_id_seq OWNER TO "oneepr-local-user";

--
-- Name: coupon_uses_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.coupon_uses_id_seq OWNED BY public.coupon_uses.id;


--
-- Name: criteria; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.criteria (
    id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp(3) without time zone,
    help_text text,
    input_type public.criteria_input_type,
    mode public.criteria_mode NOT NULL,
    title text,
    type public.criteria_type NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    country_id integer NOT NULL,
    packaging_service_id integer,
    required_information_id integer,
    calculator_type public.criteria_calculator_type,
    obligation_check_section_id bigint
);


ALTER TABLE public.criteria OWNER TO "oneepr-local-user";

--
-- Name: COLUMN criteria.obligation_check_section_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.criteria.obligation_check_section_id IS 'Foreign key to the obligation_check_section table, grouping this criterion within a section.';


--
-- Name: criteria_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.criteria_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.criteria_id_seq OWNER TO "oneepr-local-user";

--
-- Name: criteria_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.criteria_id_seq OWNED BY public.criteria.id;


--
-- Name: criteria_option; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.criteria_option (
    id integer NOT NULL,
    criteria_id integer NOT NULL,
    value text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    option_value text NOT NULL,
    option_to_value text,
    optional_criteria_id integer
);


ALTER TABLE public.criteria_option OWNER TO "oneepr-local-user";

--
-- Name: COLUMN criteria_option.optional_criteria_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.criteria_option.optional_criteria_id IS 'Foreign key to the criteria table, used to link an option to a conditional follow-up question.';


--
-- Name: criteria_option_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.criteria_option_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.criteria_option_id_seq OWNER TO "oneepr-local-user";

--
-- Name: criteria_option_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.criteria_option_id_seq OWNED BY public.criteria_option.id;


--
-- Name: customer; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer (
    id integer NOT NULL,
    type public.enum_customer_type DEFAULT 'REGULAR'::public.enum_customer_type NOT NULL,
    first_name text NOT NULL,
    last_name text NOT NULL,
    salutation text,
    email text NOT NULL,
    user_id integer NOT NULL,
    is_active boolean,
    document_id integer,
    id_stripe text,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    company_name text,
    language text,
    currency text
);


ALTER TABLE public.customer OWNER TO "oneepr-local-user";

--
-- Name: customer_activities; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_activities (
    id integer NOT NULL,
    type public.customer_activity_type_enum NOT NULL,
    metadata jsonb,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp with time zone,
    customer_id integer NOT NULL
);


ALTER TABLE public.customer_activities OWNER TO "oneepr-local-user";

--
-- Name: TABLE customer_activities; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON TABLE public.customer_activities IS 'Stores records of various activities performed by or related to customers.';


--
-- Name: COLUMN customer_activities.id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.id IS 'Primary key for the customer activity.';


--
-- Name: COLUMN customer_activities.type; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.type IS 'The type of activity that occurred.';


--
-- Name: COLUMN customer_activities.metadata; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.metadata IS 'JSONB column to store arbitrary key-value data related to the activity.';


--
-- Name: COLUMN customer_activities.created_at; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.created_at IS 'Timestamp of when the activity was created.';


--
-- Name: COLUMN customer_activities.updated_at; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.updated_at IS 'Timestamp of when the activity was last updated.';


--
-- Name: COLUMN customer_activities.deleted_at; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.deleted_at IS 'Timestamp of when the activity was soft-deleted.';


--
-- Name: COLUMN customer_activities.customer_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.customer_activities.customer_id IS 'Foreign key referencing the customer associated with this activity.';


--
-- Name: customer_activities_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_activities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_activities_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_activities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_activities_id_seq OWNED BY public.customer_activities.id;


--
-- Name: customer_commitment; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_commitment (
    id integer NOT NULL,
    customer_email text,
    country_code text NOT NULL,
    year integer NOT NULL,
    commitment jsonb NOT NULL,
    service_setup jsonb,
    is_license_required boolean DEFAULT false NOT NULL,
    blame jsonb NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    shopping_cart_id text
);


ALTER TABLE public.customer_commitment OWNER TO "oneepr-local-user";

--
-- Name: customer_commitment_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_commitment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_commitment_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_commitment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_commitment_id_seq OWNED BY public.customer_commitment.id;


--
-- Name: customer_consent; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_consent (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    consent_id integer NOT NULL,
    given boolean NOT NULL,
    "givenAt" timestamp(3) without time zone,
    "revokedAt" timestamp(3) without time zone,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.customer_consent OWNER TO "oneepr-local-user";

--
-- Name: customer_consent_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_consent_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_consent_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_consent_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_consent_id_seq OWNED BY public.customer_consent.id;


--
-- Name: customer_document; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_document (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    document_url text NOT NULL,
    status text NOT NULL
);


ALTER TABLE public.customer_document OWNER TO "oneepr-local-user";

--
-- Name: customer_document_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_document_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_document_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_document_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_document_id_seq OWNED BY public.customer_document.id;


--
-- Name: customer_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_id_seq OWNED BY public.customer.id;


--
-- Name: customer_invitation; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_invitation (
    id integer NOT NULL,
    comission_date timestamp(3) without time zone NOT NULL,
    product text NOT NULL,
    comission numeric(65,30) NOT NULL,
    order_number text NOT NULL,
    lead_source text NOT NULL,
    customer_id integer NOT NULL,
    invited_customer_id integer
);


ALTER TABLE public.customer_invitation OWNER TO "oneepr-local-user";

--
-- Name: customer_invitation_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_invitation_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_invitation_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_invitation_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_invitation_id_seq OWNED BY public.customer_invitation.id;


--
-- Name: customer_invite_token; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_invite_token (
    id integer NOT NULL,
    token text NOT NULL,
    share_link text NOT NULL,
    customer_id integer,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    expiration_date date
);


ALTER TABLE public.customer_invite_token OWNER TO "oneepr-local-user";

--
-- Name: customer_invite_token_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_invite_token_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_invite_token_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_invite_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_invite_token_id_seq OWNED BY public.customer_invite_token.id;


--
-- Name: customer_phone; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_phone (
    id integer NOT NULL,
    phone_number text NOT NULL,
    customer_id integer,
    created_at timestamp(3) without time zone NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    phone_type text DEFAULT 'PHONE'::text NOT NULL
);


ALTER TABLE public.customer_phone OWNER TO "oneepr-local-user";

--
-- Name: customer_phone_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_phone_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_phone_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_phone_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_phone_id_seq OWNED BY public.customer_phone.id;


--
-- Name: customer_tutorial; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.customer_tutorial (
    id integer NOT NULL,
    customer_id integer NOT NULL,
    service_type public.enum_contract_type NOT NULL,
    is_finished boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.customer_tutorial OWNER TO "oneepr-local-user";

--
-- Name: customer_tutorial_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.customer_tutorial_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.customer_tutorial_id_seq OWNER TO "oneepr-local-user";

--
-- Name: customer_tutorial_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.customer_tutorial_id_seq OWNED BY public.customer_tutorial.id;


--
-- Name: decline; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.decline (
    id integer NOT NULL,
    title text NOT NULL,
    license_required_information_id integer,
    license_volume_report_id integer,
    license_volume_report_item_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.decline OWNER TO "oneepr-local-user";

--
-- Name: TABLE decline; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON TABLE public.decline IS 'Stores decline events for various items like required information or volume reports.';


--
-- Name: COLUMN decline.title; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.decline.title IS 'A title or summary for the decline event.';


--
-- Name: COLUMN decline.license_required_information_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.decline.license_required_information_id IS 'Foreign key linking to a declined required information request.';


--
-- Name: COLUMN decline.license_volume_report_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.decline.license_volume_report_id IS 'Foreign key linking to a declined volume report.';


--
-- Name: COLUMN decline.license_volume_report_item_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.decline.license_volume_report_item_id IS 'Foreign key linking to a specific declined item within a volume report.';


--
-- Name: decline_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.decline_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.decline_id_seq OWNER TO "oneepr-local-user";

--
-- Name: decline_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.decline_id_seq OWNED BY public.decline.id;


--
-- Name: decline_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.decline_reason (
    id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    decline_id integer NOT NULL,
    reason_id integer NOT NULL
);


ALTER TABLE public.decline_reason OWNER TO "oneepr-local-user";

--
-- Name: decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.decline_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.decline_reason_id_seq OWNED BY public.decline_reason.id;


--
-- Name: decline_to_decline_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.decline_to_decline_reason (
    id integer NOT NULL,
    decline_id integer NOT NULL,
    reason_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.decline_to_decline_reason OWNER TO "oneepr-local-user";

--
-- Name: decline_to_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.decline_to_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.decline_to_decline_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: decline_to_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.decline_to_decline_reason_id_seq OWNED BY public.decline_to_decline_reason.id;


--
-- Name: file; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.file (
    id text NOT NULL,
    user_id text NOT NULL,
    name text NOT NULL,
    original_name text NOT NULL,
    extension text NOT NULL,
    size text NOT NULL,
    type public.enum_file_type NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    required_information_id integer,
    contract_id integer,
    certificate_id integer,
    license_id integer,
    termination_id integer,
    general_information_id integer,
    third_party_invoice_id integer,
    marketing_material_id integer,
    partner_contract_id integer,
    order_id integer
);


ALTER TABLE public.file OWNER TO "oneepr-local-user";

--
-- Name: files; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.files (
    id text NOT NULL,
    name text NOT NULL,
    extension text NOT NULL,
    size text NOT NULL,
    creator_type text NOT NULL,
    document_type text NOT NULL,
    user_id text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone,
    original_name text NOT NULL,
    country_id integer
);


ALTER TABLE public.files OWNER TO "oneepr-local-user";

--
-- Name: flyway_schema_history; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.flyway_schema_history (
    installed_rank integer NOT NULL,
    version character varying(50),
    description character varying(200) NOT NULL,
    type character varying(20) NOT NULL,
    script character varying(1000) NOT NULL,
    checksum integer,
    installed_by character varying(100) NOT NULL,
    installed_on timestamp without time zone DEFAULT now() NOT NULL,
    execution_time integer NOT NULL,
    success boolean NOT NULL
);


ALTER TABLE public.flyway_schema_history OWNER TO "oneepr-local-user";

--
-- Name: fraction_icon; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.fraction_icon (
    id integer NOT NULL,
    file_id text NOT NULL,
    image_url text DEFAULT ''::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.fraction_icon OWNER TO "oneepr-local-user";

--
-- Name: fraction_icon_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.fraction_icon_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.fraction_icon_id_seq OWNER TO "oneepr-local-user";

--
-- Name: fraction_icon_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.fraction_icon_id_seq OWNED BY public.fraction_icon.id;


--
-- Name: general_information; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.general_information (
    id integer NOT NULL,
    setup_general_information_id integer NOT NULL,
    contract_id integer NOT NULL,
    type public.enum_general_information_type NOT NULL,
    status public.enum_general_information_status DEFAULT 'OPEN'::public.enum_general_information_status NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    question text,
    file_id text,
    answer text
);


ALTER TABLE public.general_information OWNER TO "oneepr-local-user";

--
-- Name: general_information_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.general_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.general_information_id_seq OWNER TO "oneepr-local-user";

--
-- Name: general_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.general_information_id_seq OWNED BY public.general_information.id;


--
-- Name: license; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license (
    id integer NOT NULL,
    contract_id integer NOT NULL,
    registration_number text NOT NULL,
    registration_status public.enum_license_registration_status DEFAULT 'PENDING'::public.enum_license_registration_status NOT NULL,
    clerk_control_status public.enum_license_clerk_control_status DEFAULT 'PENDING'::public.enum_license_clerk_control_status NOT NULL,
    contract_status public.enum_license_contract_status DEFAULT 'ACTIVE'::public.enum_license_contract_status NOT NULL,
    country_id integer NOT NULL,
    country_code text NOT NULL,
    country_name text NOT NULL,
    country_flag text NOT NULL,
    year integer NOT NULL,
    start_date timestamp(3) without time zone NOT NULL,
    end_date timestamp(3) without time zone,
    termination_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    registration_and_termination_monday_ref integer
);


ALTER TABLE public.license OWNER TO "oneepr-local-user";

--
-- Name: license_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_id_seq OWNED BY public.license.id;


--
-- Name: license_other_cost; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_other_cost (
    id integer NOT NULL,
    setup_other_cost_id integer NOT NULL,
    license_id integer NOT NULL,
    name text NOT NULL,
    price integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_other_cost OWNER TO "oneepr-local-user";

--
-- Name: license_other_cost_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_other_cost_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_other_cost_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_other_cost_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_other_cost_id_seq OWNED BY public.license_other_cost.id;


--
-- Name: license_packaging_service; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_packaging_service (
    id integer NOT NULL,
    setup_packaging_service_id integer NOT NULL,
    license_id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    obliged boolean DEFAULT false NOT NULL
);


ALTER TABLE public.license_packaging_service OWNER TO "oneepr-local-user";

--
-- Name: license_packaging_service_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_packaging_service_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_packaging_service_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_packaging_service_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_packaging_service_id_seq OWNED BY public.license_packaging_service.id;


--
-- Name: license_price_list; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_price_list (
    id integer NOT NULL,
    setup_price_list_id integer NOT NULL,
    license_id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    condition_type text NOT NULL,
    condition_type_value text NOT NULL,
    start_date timestamp(3) without time zone NOT NULL,
    end_date timestamp(3) without time zone NOT NULL,
    basic_price integer,
    minimum_price integer,
    registration_fee integer,
    handling_fee integer,
    variable_handling_fee double precision,
    thresholds jsonb,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.license_price_list OWNER TO "oneepr-local-user";

--
-- Name: license_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_price_list_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_price_list_id_seq OWNED BY public.license_price_list.id;


--
-- Name: license_report_set; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_report_set (
    id integer NOT NULL,
    setup_report_set_id integer NOT NULL,
    license_packaging_service_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_report_set OWNER TO "oneepr-local-user";

--
-- Name: license_report_set_frequency; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_report_set_frequency (
    id integer NOT NULL,
    setup_report_set_frequency_id integer NOT NULL,
    license_packaging_service_id integer NOT NULL,
    rhythm public.enum_license_report_set_rhythm NOT NULL,
    frequency jsonb NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_report_set_frequency OWNER TO "oneepr-local-user";

--
-- Name: license_report_set_frequency_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_report_set_frequency_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_report_set_frequency_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_report_set_frequency_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_report_set_frequency_id_seq OWNED BY public.license_report_set_frequency.id;


--
-- Name: license_report_set_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_report_set_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_report_set_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_report_set_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_report_set_id_seq OWNED BY public.license_report_set.id;


--
-- Name: license_representative_tier; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_representative_tier (
    id integer NOT NULL,
    setup_representative_tier_id integer NOT NULL,
    license_id integer NOT NULL,
    name text NOT NULL,
    price integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_representative_tier OWNER TO "oneepr-local-user";

--
-- Name: license_representative_tier_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_representative_tier_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_representative_tier_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_representative_tier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_representative_tier_id_seq OWNED BY public.license_representative_tier.id;


--
-- Name: license_required_information; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_required_information (
    id integer NOT NULL,
    setup_required_information_id integer,
    license_id integer,
    type public.enum_license_required_information_type NOT NULL,
    status public.enum_license_required_information_status DEFAULT 'OPEN'::public.enum_license_required_information_status NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    question text,
    file_id text,
    answer text,
    kind public.enum_license_required_information_kind DEFAULT 'REQUIRED_INFORMATION'::public.enum_license_required_information_kind NOT NULL,
    contract_id integer,
    setup_general_information_id integer
);


ALTER TABLE public.license_required_information OWNER TO "oneepr-local-user";

--
-- Name: license_required_information_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_required_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_required_information_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_required_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_required_information_id_seq OWNED BY public.license_required_information.id;


--
-- Name: license_third_party_invoice; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_third_party_invoice (
    id integer NOT NULL,
    title text NOT NULL,
    price integer NOT NULL,
    issued_at timestamp(3) without time zone NOT NULL,
    status public.enum_license_third_party_invoice_status DEFAULT 'OPEN'::public.enum_license_third_party_invoice_status NOT NULL,
    license_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    due_date timestamp(3) without time zone NOT NULL,
    issuer public."LicenseThirdPartyInvoiceIssuer" NOT NULL,
    third_party_invoice_monday_ref integer
);


ALTER TABLE public.license_third_party_invoice OWNER TO "oneepr-local-user";

--
-- Name: license_third_party_invoice_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_third_party_invoice_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_third_party_invoice_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_third_party_invoice_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_third_party_invoice_id_seq OWNED BY public.license_third_party_invoice.id;


--
-- Name: license_volume_report; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_volume_report (
    id integer NOT NULL,
    license_packaging_service_id integer NOT NULL,
    status public.enum_license_volume_report_status DEFAULT 'OPEN'::public.enum_license_volume_report_status NOT NULL,
    year integer NOT NULL,
    "interval" text NOT NULL,
    report_table jsonb NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    volume_report_monday_ref integer,
    stage text
);


ALTER TABLE public.license_volume_report OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_decline_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_volume_report_decline_reason (
    id integer NOT NULL,
    license_volume_report_error_id integer NOT NULL,
    report_decline_reason_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_volume_report_decline_reason OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_volume_report_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_volume_report_decline_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_volume_report_decline_reason_id_seq OWNED BY public.license_volume_report_decline_reason.id;


--
-- Name: license_volume_report_error; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_volume_report_error (
    id integer NOT NULL,
    license_volume_report_id integer,
    license_volume_report_item_id integer,
    description text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.license_volume_report_error OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_error_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_volume_report_error_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_volume_report_error_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_error_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_volume_report_error_id_seq OWNED BY public.license_volume_report_error.id;


--
-- Name: license_volume_report_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_volume_report_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_volume_report_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_volume_report_id_seq OWNED BY public.license_volume_report.id;


--
-- Name: license_volume_report_item; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.license_volume_report_item (
    id integer NOT NULL,
    license_volume_report_id integer NOT NULL,
    setup_fraction_id integer NOT NULL,
    setup_column_id integer DEFAULT 0 NOT NULL,
    value integer NOT NULL,
    price integer DEFAULT 0 NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    setup_column_code text,
    setup_fraction_code text
);


ALTER TABLE public.license_volume_report_item OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_item_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.license_volume_report_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.license_volume_report_item_id_seq OWNER TO "oneepr-local-user";

--
-- Name: license_volume_report_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.license_volume_report_item_id_seq OWNED BY public.license_volume_report_item.id;


--
-- Name: marketing_material; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.marketing_material (
    id integer NOT NULL,
    name text NOT NULL,
    start_date date,
    end_date date,
    category public.enum_marketing_material_category NOT NULL,
    partner_restriction public.enum_marketing_material_partner_restriction NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.marketing_material OWNER TO "oneepr-local-user";

--
-- Name: marketing_material_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.marketing_material_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.marketing_material_id_seq OWNER TO "oneepr-local-user";

--
-- Name: marketing_material_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.marketing_material_id_seq OWNED BY public.marketing_material.id;


--
-- Name: marketing_material_partner; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.marketing_material_partner (
    id integer NOT NULL,
    marketing_material_id integer NOT NULL,
    partner_id integer NOT NULL
);


ALTER TABLE public.marketing_material_partner OWNER TO "oneepr-local-user";

--
-- Name: marketing_material_partner_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.marketing_material_partner_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.marketing_material_partner_id_seq OWNER TO "oneepr-local-user";

--
-- Name: marketing_material_partner_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.marketing_material_partner_id_seq OWNED BY public.marketing_material_partner.id;


--
-- Name: notifications; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.notifications (
    id text NOT NULL,
    user_id integer NOT NULL,
    service public."Services" DEFAULT 'CUSTOMER'::public."Services" NOT NULL,
    subject text NOT NULL,
    message text NOT NULL,
    type public."TypeNotifications" DEFAULT 'EMAIL'::public."TypeNotifications" NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.notifications OWNER TO "oneepr-local-user";

--
-- Name: obligation_check_section; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.obligation_check_section (
    id bigint NOT NULL,
    title text NOT NULL,
    display_order integer NOT NULL,
    country_id integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL
);


ALTER TABLE public.obligation_check_section OWNER TO "oneepr-local-user";

--
-- Name: TABLE obligation_check_section; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON TABLE public.obligation_check_section IS 'Stores sections or tabs used to group questions in a country''s obligation check.';


--
-- Name: obligation_check_section_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.obligation_check_section_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.obligation_check_section_id_seq OWNER TO "oneepr-local-user";

--
-- Name: obligation_check_section_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.obligation_check_section_id_seq OWNED BY public.obligation_check_section.id;


--
-- Name: one_epr_role; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.one_epr_role (
    id integer NOT NULL,
    name text NOT NULL,
    display_name text NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.one_epr_role OWNER TO "oneepr-local-user";

--
-- Name: one_epr_role_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.one_epr_role_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.one_epr_role_id_seq OWNER TO "oneepr-local-user";

--
-- Name: one_epr_role_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.one_epr_role_id_seq OWNED BY public.one_epr_role.id;


--
-- Name: one_epr_user; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.one_epr_user (
    id integer NOT NULL,
    first_name text,
    last_name text,
    name text NOT NULL,
    email character varying(255) NOT NULL,
    password text,
    is_active boolean DEFAULT false NOT NULL,
    role_id integer,
    token_verify text,
    token_expiration timestamp without time zone,
    token_magic_link text,
    token_create_password text,
    token_attempts integer DEFAULT 0 NOT NULL,
    block_time timestamp without time zone,
    status public."Status" DEFAULT 'NOT_VERIFIED'::public."Status" NOT NULL,
    created_at timestamp without time zone,
    updated_at timestamp without time zone,
    deleted_at timestamp without time zone
);


ALTER TABLE public.one_epr_user OWNER TO "oneepr-local-user";

--
-- Name: one_epr_user_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.one_epr_user_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.one_epr_user_id_seq OWNER TO "oneepr-local-user";

--
-- Name: one_epr_user_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.one_epr_user_id_seq OWNED BY public.one_epr_user.id;


--
-- Name: other_cost; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.other_cost (
    id integer NOT NULL,
    name text NOT NULL,
    price integer NOT NULL,
    country_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.other_cost OWNER TO "oneepr-local-user";

--
-- Name: other_cost_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.other_cost_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.other_cost_id_seq OWNER TO "oneepr-local-user";

--
-- Name: other_cost_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.other_cost_id_seq OWNED BY public.other_cost.id;


--
-- Name: packaging_service; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.packaging_service (
    id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    country_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.packaging_service OWNER TO "oneepr-local-user";

--
-- Name: packaging_service_criteria_option; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.packaging_service_criteria_option (
    packaging_service_id integer NOT NULL,
    criteria_option_id integer NOT NULL
);


ALTER TABLE public.packaging_service_criteria_option OWNER TO "oneepr-local-user";

--
-- Name: TABLE packaging_service_criteria_option; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON TABLE public.packaging_service_criteria_option IS 'Join table for the many-to-many relationship between packaging_service and criteria_option.';


--
-- Name: packaging_service_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.packaging_service_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.packaging_service_id_seq OWNER TO "oneepr-local-user";

--
-- Name: packaging_service_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.packaging_service_id_seq OWNED BY public.packaging_service.id;


--
-- Name: partner; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.partner (
    id integer NOT NULL,
    first_name text NOT NULL,
    last_name text NOT NULL,
    email text NOT NULL,
    user_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    commission_mode text,
    commission_percentage integer,
    checked boolean DEFAULT false,
    no_provision_negotiated boolean DEFAULT false,
    payout_cycle text,
    status public."PartnerStatus" DEFAULT 'NO_UPDATES'::public."PartnerStatus"
);


ALTER TABLE public.partner OWNER TO "oneepr-local-user";

--
-- Name: partner_banking; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.partner_banking (
    id integer NOT NULL,
    partner_id integer,
    business_identifier_code text,
    international_account_number text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.partner_banking OWNER TO "oneepr-local-user";

--
-- Name: partner_banking_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.partner_banking_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.partner_banking_id_seq OWNER TO "oneepr-local-user";

--
-- Name: partner_banking_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.partner_banking_id_seq OWNED BY public.partner_banking.id;


--
-- Name: partner_contract; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.partner_contract (
    id integer NOT NULL,
    partner_id integer NOT NULL,
    status public."PartnerContractStatus" DEFAULT 'DRAFT'::public."PartnerContractStatus" NOT NULL,
    agreed_on timestamp(3) without time zone,
    start_date timestamp(3) without time zone NOT NULL,
    end_date timestamp(3) without time zone,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.partner_contract OWNER TO "oneepr-local-user";

--
-- Name: partner_contract_change; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.partner_contract_change (
    id integer NOT NULL,
    partner_contract_id integer NOT NULL,
    change_type public."PartnerContractChangeType" NOT NULL,
    change_description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.partner_contract_change OWNER TO "oneepr-local-user";

--
-- Name: partner_contract_change_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.partner_contract_change_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.partner_contract_change_id_seq OWNER TO "oneepr-local-user";

--
-- Name: partner_contract_change_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.partner_contract_change_id_seq OWNED BY public.partner_contract_change.id;


--
-- Name: partner_contract_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.partner_contract_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.partner_contract_id_seq OWNER TO "oneepr-local-user";

--
-- Name: partner_contract_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.partner_contract_id_seq OWNED BY public.partner_contract.id;


--
-- Name: partner_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.partner_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.partner_id_seq OWNER TO "oneepr-local-user";

--
-- Name: partner_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.partner_id_seq OWNED BY public.partner.id;


--
-- Name: password_reset_request; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.password_reset_request (
    id integer NOT NULL,
    email text NOT NULL,
    user_id integer NOT NULL,
    token text NOT NULL,
    status character varying(255) DEFAULT 'PENDING'::character varying,
    callback_url text,
    expires_at timestamp without time zone
);


ALTER TABLE public.password_reset_request OWNER TO "oneepr-local-user";

--
-- Name: password_reset_request_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.password_reset_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.password_reset_request_id_seq OWNER TO "oneepr-local-user";

--
-- Name: password_reset_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.password_reset_request_id_seq OWNED BY public.password_reset_request.id;


--
-- Name: price_list; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.price_list (
    id integer NOT NULL,
    type public.price_list_type NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    start_date timestamp(3) without time zone NOT NULL,
    end_date timestamp(3) without time zone NOT NULL,
    basic_price integer,
    minimum_price integer,
    registration_fee integer,
    variable_handling_fee double precision,
    price integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    condition_type public."PriceListConditionType" NOT NULL,
    condition_type_value text NOT NULL,
    handling_fee integer,
    thresholds jsonb
);


ALTER TABLE public.price_list OWNER TO "oneepr-local-user";

--
-- Name: price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.price_list_id_seq OWNER TO "oneepr-local-user";

--
-- Name: price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.price_list_id_seq OWNED BY public.price_list.id;


--
-- Name: reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.reason (
    id integer NOT NULL,
    title text NOT NULL,
    value text NOT NULL,
    type text NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.reason OWNER TO "oneepr-local-user";

--
-- Name: reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.reason_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: recommended_country; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.recommended_country (
    id integer NOT NULL,
    name text NOT NULL,
    customer_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.recommended_country OWNER TO "oneepr-local-user";

--
-- Name: recommended_country_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.recommended_country_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.recommended_country_id_seq OWNER TO "oneepr-local-user";

--
-- Name: recommended_country_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.recommended_country_id_seq OWNED BY public.recommended_country.id;


--
-- Name: refresh_token; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.refresh_token (
    id integer NOT NULL,
    user_id integer NOT NULL,
    token text NOT NULL,
    created_at timestamp without time zone
);


ALTER TABLE public.refresh_token OWNER TO "oneepr-local-user";

--
-- Name: refresh_token_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.refresh_token_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.refresh_token_id_seq OWNER TO "oneepr-local-user";

--
-- Name: refresh_token_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.refresh_token_id_seq OWNED BY public.refresh_token.id;


--
-- Name: report_decline_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_decline_reason (
    id integer NOT NULL,
    title text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.report_decline_reason OWNER TO "oneepr-local-user";

--
-- Name: report_decline_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_decline_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_decline_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_decline_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_decline_reason_id_seq OWNED BY public.report_decline_reason.id;


--
-- Name: report_set; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set (
    id integer NOT NULL,
    name text NOT NULL,
    mode public.report_set_mode NOT NULL,
    type public.report_set_type NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    packaging_service_id integer NOT NULL,
    sheet_file_description text,
    sheet_file_id text
);


ALTER TABLE public.report_set OWNER TO "oneepr-local-user";

--
-- Name: report_set_column; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_column (
    id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    unit_type public."ReportSetColumnUnitType" NOT NULL,
    report_set_id integer NOT NULL,
    parent_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    code text NOT NULL,
    level integer DEFAULT 1 NOT NULL,
    "order" integer DEFAULT 1 NOT NULL,
    parent_code text
);


ALTER TABLE public.report_set_column OWNER TO "oneepr-local-user";

--
-- Name: report_set_column_fraction; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_column_fraction (
    id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    column_code text NOT NULL,
    fraction_code text NOT NULL
);


ALTER TABLE public.report_set_column_fraction OWNER TO "oneepr-local-user";

--
-- Name: report_set_column_fraction_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_column_fraction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_column_fraction_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_column_fraction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_column_fraction_id_seq OWNED BY public.report_set_column_fraction.id;


--
-- Name: report_set_column_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_column_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_column_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_column_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_column_id_seq OWNED BY public.report_set_column.id;


--
-- Name: report_set_fraction; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_fraction (
    id integer NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    report_set_id integer NOT NULL,
    parent_id integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    icon text DEFAULT 'aluminium'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    code text NOT NULL,
    fraction_icon_id integer,
    level integer DEFAULT 1 NOT NULL,
    "order" integer DEFAULT 1 NOT NULL,
    parent_code text,
    has_second_level boolean DEFAULT false NOT NULL,
    has_third_level boolean DEFAULT false NOT NULL
);


ALTER TABLE public.report_set_fraction OWNER TO "oneepr-local-user";

--
-- Name: report_set_fraction_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_fraction_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_fraction_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_fraction_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_fraction_id_seq OWNED BY public.report_set_fraction.id;


--
-- Name: report_set_frequency; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_frequency (
    id integer NOT NULL,
    rhythm public.report_set_rhythm NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    frequency jsonb NOT NULL,
    packaging_service_id integer NOT NULL
);


ALTER TABLE public.report_set_frequency OWNER TO "oneepr-local-user";

--
-- Name: report_set_frequency_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_frequency_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_frequency_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_frequency_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_frequency_id_seq OWNED BY public.report_set_frequency.id;


--
-- Name: report_set_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_id_seq OWNED BY public.report_set.id;


--
-- Name: report_set_price_list; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_price_list (
    id integer NOT NULL,
    report_set_id integer NOT NULL,
    title text NOT NULL,
    license_year integer DEFAULT 2025 NOT NULL,
    start_date timestamp(3) without time zone NOT NULL,
    end_date timestamp(3) without time zone NOT NULL,
    type public.report_set_price_list_type NOT NULL,
    fixed_price integer,
    base_price integer,
    minimum_fee integer,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.report_set_price_list OWNER TO "oneepr-local-user";

--
-- Name: report_set_price_list_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_price_list_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_price_list_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_price_list_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_price_list_id_seq OWNED BY public.report_set_price_list.id;


--
-- Name: report_set_price_list_item; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.report_set_price_list_item (
    id integer NOT NULL,
    price_list_id integer NOT NULL,
    fraction_code text NOT NULL,
    price integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.report_set_price_list_item OWNER TO "oneepr-local-user";

--
-- Name: report_set_price_list_item_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.report_set_price_list_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.report_set_price_list_item_id_seq OWNER TO "oneepr-local-user";

--
-- Name: report_set_price_list_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.report_set_price_list_item_id_seq OWNED BY public.report_set_price_list_item.id;


--
-- Name: representative_tier; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.representative_tier (
    id integer NOT NULL,
    name text NOT NULL,
    price integer NOT NULL,
    country_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.representative_tier OWNER TO "oneepr-local-user";

--
-- Name: representative_tier_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.representative_tier_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.representative_tier_id_seq OWNER TO "oneepr-local-user";

--
-- Name: representative_tier_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.representative_tier_id_seq OWNED BY public.representative_tier.id;


--
-- Name: required_information; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.required_information (
    id integer NOT NULL,
    country_id integer,
    type public.required_information_type NOT NULL,
    name text NOT NULL,
    description text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone,
    question text,
    file_id text,
    kind public.required_information_kind DEFAULT 'COUNTRY_INFORMATION'::public.required_information_kind NOT NULL
);


ALTER TABLE public.required_information OWNER TO "oneepr-local-user";

--
-- Name: required_information_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.required_information_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.required_information_id_seq OWNER TO "oneepr-local-user";

--
-- Name: required_information_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.required_information_id_seq OWNED BY public.required_information.id;


--
-- Name: required_information_packaging_service; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.required_information_packaging_service (
    required_information_id integer NOT NULL,
    packaging_service_id integer NOT NULL,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.required_information_packaging_service OWNER TO "oneepr-local-user";

--
-- Name: TABLE required_information_packaging_service; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON TABLE public.required_information_packaging_service IS 'Junction table for many-to-many relationship between required_information and packaging_service';


--
-- Name: COLUMN required_information_packaging_service.required_information_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.required_information_packaging_service.required_information_id IS 'Foreign key reference to required_information.id';


--
-- Name: COLUMN required_information_packaging_service.packaging_service_id; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.required_information_packaging_service.packaging_service_id IS 'Foreign key reference to packaging_service.id';


--
-- Name: COLUMN required_information_packaging_service.created_at; Type: COMMENT; Schema: public; Owner: oneepr-local-user
--

COMMENT ON COLUMN public.required_information_packaging_service.created_at IS 'Timestamp when the relationship was created';


--
-- Name: service_next_step; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.service_next_step (
    id integer NOT NULL,
    license_id integer,
    action_guide_id integer,
    title text NOT NULL,
    available_date timestamp(3) without time zone NOT NULL,
    deadline_date timestamp(3) without time zone NOT NULL,
    done_at timestamp(3) without time zone,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.service_next_step OWNER TO "oneepr-local-user";

--
-- Name: service_next_step_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.service_next_step_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.service_next_step_id_seq OWNER TO "oneepr-local-user";

--
-- Name: service_next_step_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.service_next_step_id_seq OWNED BY public.service_next_step.id;


--
-- Name: settings; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.settings (
    id integer NOT NULL,
    key text NOT NULL,
    value jsonb NOT NULL,
    term_or_condition_file_id text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at timestamp(3) without time zone
);


ALTER TABLE public.settings OWNER TO "oneepr-local-user";

--
-- Name: settings_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.settings_id_seq OWNER TO "oneepr-local-user";

--
-- Name: settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.settings_id_seq OWNED BY public.settings.id;


--
-- Name: shopping_cart; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.shopping_cart (
    id text NOT NULL,
    cart_json jsonb,
    email text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    invoice_id integer,
    is_churned boolean DEFAULT false NOT NULL,
    journey public.shopping_cart_journey NOT NULL,
    journey_step text,
    payment jsonb,
    status public.shopping_cart_status DEFAULT 'OPEN'::public.shopping_cart_status NOT NULL,
    total integer DEFAULT 0 NOT NULL,
    subtotal integer DEFAULT 0 NOT NULL,
    vat_percentage integer DEFAULT 0 NOT NULL,
    vat_value integer DEFAULT 0 NOT NULL,
    coupon_id integer,
    coupon_type public.type_use_coupon,
    coupon_value integer DEFAULT 0 NOT NULL,
    coupon_url_link text,
    affiliate_link text,
    affiliate_type public.type_affiliate,
    affiliate_customer_id integer,
    affiliate_partner_id integer,
    customer_id integer
);


ALTER TABLE public.shopping_cart OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_item; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.shopping_cart_item (
    id integer NOT NULL,
    shopping_cart_id text NOT NULL,
    service_type public.enum_contract_type NOT NULL,
    specification_type public.shopping_cart_item_specification_type NOT NULL,
    country_id integer NOT NULL,
    country_code text NOT NULL,
    country_name text NOT NULL,
    country_flag text NOT NULL,
    year integer NOT NULL,
    price_list jsonb NOT NULL,
    packaging_services jsonb,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    calculator jsonb,
    price integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.shopping_cart_item OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_item_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.shopping_cart_item_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.shopping_cart_item_id_seq OWNER TO "oneepr-local-user";

--
-- Name: shopping_cart_item_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.shopping_cart_item_id_seq OWNED BY public.shopping_cart_item.id;


--
-- Name: termination; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.termination (
    id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    deleted_at date,
    completed_at timestamp(3) without time zone,
    requested_at timestamp(3) without time zone NOT NULL,
    status public.enum_termination_status DEFAULT 'REQUESTED'::public.enum_termination_status NOT NULL
);


ALTER TABLE public.termination OWNER TO "oneepr-local-user";

--
-- Name: termination_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.termination_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.termination_id_seq OWNER TO "oneepr-local-user";

--
-- Name: termination_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.termination_id_seq OWNED BY public.termination.id;


--
-- Name: termination_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.termination_reason (
    id integer NOT NULL,
    created_at timestamp without time zone NOT NULL,
    updated_at timestamp without time zone NOT NULL,
    deleted_at date,
    termination integer NOT NULL,
    reason integer NOT NULL
);


ALTER TABLE public.termination_reason OWNER TO "oneepr-local-user";

--
-- Name: termination_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.termination_reason_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.termination_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: termination_termination_reason; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.termination_termination_reason (
    id integer NOT NULL,
    termination_id integer NOT NULL,
    reason_id integer NOT NULL,
    created_at timestamp with time zone NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    deleted_at date
);


ALTER TABLE public.termination_termination_reason OWNER TO "oneepr-local-user";

--
-- Name: termination_termination_reason_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.termination_termination_reason_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.termination_termination_reason_id_seq OWNER TO "oneepr-local-user";

--
-- Name: termination_termination_reason_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.termination_termination_reason_id_seq OWNED BY public.termination_termination_reason.id;


--
-- Name: upload_file_history; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.upload_file_history (
    id integer NOT NULL,
    file_name text NOT NULL,
    file_url text NOT NULL,
    broker_id integer NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    deleted_at timestamp(3) without time zone,
    updated_at timestamp(3) without time zone
);


ALTER TABLE public.upload_file_history OWNER TO "oneepr-local-user";

--
-- Name: upload_file_history_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.upload_file_history_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.upload_file_history_id_seq OWNER TO "oneepr-local-user";

--
-- Name: upload_file_history_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.upload_file_history_id_seq OWNED BY public.upload_file_history.id;


--
-- Name: user_access_request; Type: TABLE; Schema: public; Owner: oneepr-local-user
--

CREATE TABLE public.user_access_request (
    id integer NOT NULL,
    user_id integer,
    user_email character varying(255),
    requester_id integer NOT NULL,
    status character varying(255) DEFAULT 'PENDING'::character varying,
    token text NOT NULL,
    callback_url text,
    is_active boolean NOT NULL,
    created_at timestamp without time zone,
    expires_at timestamp without time zone,
    updated_at timestamp without time zone,
    wrong_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public.user_access_request OWNER TO "oneepr-local-user";

--
-- Name: user_access_request_id_seq; Type: SEQUENCE; Schema: public; Owner: oneepr-local-user
--

CREATE SEQUENCE public.user_access_request_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_access_request_id_seq OWNER TO "oneepr-local-user";

--
-- Name: user_access_request_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: oneepr-local-user
--

ALTER SEQUENCE public.user_access_request_id_seq OWNED BY public.user_access_request.id;


--
-- Name: action_guide id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide ALTER COLUMN id SET DEFAULT nextval('public.action_guide_id_seq'::regclass);


--
-- Name: action_guide_price_list id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list ALTER COLUMN id SET DEFAULT nextval('public.action_guide_price_list_id_seq'::regclass);


--
-- Name: broker id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker ALTER COLUMN id SET DEFAULT nextval('public.broker_id_seq'::regclass);


--
-- Name: broker_company id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company ALTER COLUMN id SET DEFAULT nextval('public.broker_company_id_seq'::regclass);


--
-- Name: broker_company_order id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company_order ALTER COLUMN id SET DEFAULT nextval('public.broker_company_order_id_seq'::regclass);


--
-- Name: certificate id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.certificate ALTER COLUMN id SET DEFAULT nextval('public.certificate_id_seq'::regclass);


--
-- Name: cluster id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster ALTER COLUMN id SET DEFAULT nextval('public.cluster_id_seq'::regclass);


--
-- Name: cluster_customers id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster_customers ALTER COLUMN id SET DEFAULT nextval('public.cluster_customers_id_seq'::regclass);


--
-- Name: commission id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.commission ALTER COLUMN id SET DEFAULT nextval('public.commission_id_seq'::regclass);


--
-- Name: company id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company ALTER COLUMN id SET DEFAULT nextval('public.company_id_seq'::regclass);


--
-- Name: company_address id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_address ALTER COLUMN id SET DEFAULT nextval('public.company_address_id_seq'::regclass);


--
-- Name: company_contact id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_contact ALTER COLUMN id SET DEFAULT nextval('public.company_contact_id_seq'::regclass);


--
-- Name: company_email id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_email ALTER COLUMN id SET DEFAULT nextval('public.company_email_id_seq'::regclass);


--
-- Name: consent id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.consent ALTER COLUMN id SET DEFAULT nextval('public.consent_id_seq'::regclass);


--
-- Name: contract id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.contract ALTER COLUMN id SET DEFAULT nextval('public.contract_id_seq'::regclass);


--
-- Name: country id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country ALTER COLUMN id SET DEFAULT nextval('public.country_id_seq'::regclass);


--
-- Name: country_follower id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_follower ALTER COLUMN id SET DEFAULT nextval('public.country_follower_id_seq'::regclass);


--
-- Name: country_price_list id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_price_list ALTER COLUMN id SET DEFAULT nextval('public.country_price_list_id_seq'::regclass);


--
-- Name: coupon id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon ALTER COLUMN id SET DEFAULT nextval('public.coupon_id_seq'::regclass);


--
-- Name: coupon_customer id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_customer ALTER COLUMN id SET DEFAULT nextval('public.coupon_customer_id_seq'::regclass);


--
-- Name: coupon_partners id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_partners ALTER COLUMN id SET DEFAULT nextval('public.coupon_partners_id_seq'::regclass);


--
-- Name: coupon_uses id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_uses ALTER COLUMN id SET DEFAULT nextval('public.coupon_uses_id_seq'::regclass);


--
-- Name: criteria id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria ALTER COLUMN id SET DEFAULT nextval('public.criteria_id_seq'::regclass);


--
-- Name: criteria_option id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria_option ALTER COLUMN id SET DEFAULT nextval('public.criteria_option_id_seq'::regclass);


--
-- Name: customer id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer ALTER COLUMN id SET DEFAULT nextval('public.customer_id_seq'::regclass);


--
-- Name: customer_activities id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_activities ALTER COLUMN id SET DEFAULT nextval('public.customer_activities_id_seq'::regclass);


--
-- Name: customer_commitment id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_commitment ALTER COLUMN id SET DEFAULT nextval('public.customer_commitment_id_seq'::regclass);


--
-- Name: customer_consent id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_consent ALTER COLUMN id SET DEFAULT nextval('public.customer_consent_id_seq'::regclass);


--
-- Name: customer_document id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_document ALTER COLUMN id SET DEFAULT nextval('public.customer_document_id_seq'::regclass);


--
-- Name: customer_invitation id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invitation ALTER COLUMN id SET DEFAULT nextval('public.customer_invitation_id_seq'::regclass);


--
-- Name: customer_invite_token id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invite_token ALTER COLUMN id SET DEFAULT nextval('public.customer_invite_token_id_seq'::regclass);


--
-- Name: customer_phone id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_phone ALTER COLUMN id SET DEFAULT nextval('public.customer_phone_id_seq'::regclass);


--
-- Name: customer_tutorial id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_tutorial ALTER COLUMN id SET DEFAULT nextval('public.customer_tutorial_id_seq'::regclass);


--
-- Name: decline id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline ALTER COLUMN id SET DEFAULT nextval('public.decline_id_seq'::regclass);


--
-- Name: decline_reason id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_reason ALTER COLUMN id SET DEFAULT nextval('public.decline_reason_id_seq'::regclass);


--
-- Name: decline_to_decline_reason id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.decline_to_decline_reason_id_seq'::regclass);


--
-- Name: fraction_icon id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.fraction_icon ALTER COLUMN id SET DEFAULT nextval('public.fraction_icon_id_seq'::regclass);


--
-- Name: general_information id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.general_information ALTER COLUMN id SET DEFAULT nextval('public.general_information_id_seq'::regclass);


--
-- Name: license id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license ALTER COLUMN id SET DEFAULT nextval('public.license_id_seq'::regclass);


--
-- Name: license_other_cost id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_other_cost ALTER COLUMN id SET DEFAULT nextval('public.license_other_cost_id_seq'::regclass);


--
-- Name: license_packaging_service id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_packaging_service ALTER COLUMN id SET DEFAULT nextval('public.license_packaging_service_id_seq'::regclass);


--
-- Name: license_price_list id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_price_list ALTER COLUMN id SET DEFAULT nextval('public.license_price_list_id_seq'::regclass);


--
-- Name: license_report_set id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set ALTER COLUMN id SET DEFAULT nextval('public.license_report_set_id_seq'::regclass);


--
-- Name: license_report_set_frequency id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency ALTER COLUMN id SET DEFAULT nextval('public.license_report_set_frequency_id_seq'::regclass);


--
-- Name: license_representative_tier id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_representative_tier ALTER COLUMN id SET DEFAULT nextval('public.license_representative_tier_id_seq'::regclass);


--
-- Name: license_required_information id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_required_information ALTER COLUMN id SET DEFAULT nextval('public.license_required_information_id_seq'::regclass);


--
-- Name: license_third_party_invoice id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice ALTER COLUMN id SET DEFAULT nextval('public.license_third_party_invoice_id_seq'::regclass);


--
-- Name: license_volume_report id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_id_seq'::regclass);


--
-- Name: license_volume_report_decline_reason id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_decline_reason_id_seq'::regclass);


--
-- Name: license_volume_report_error id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_error_id_seq'::regclass);


--
-- Name: license_volume_report_item id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item ALTER COLUMN id SET DEFAULT nextval('public.license_volume_report_item_id_seq'::regclass);


--
-- Name: marketing_material id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material ALTER COLUMN id SET DEFAULT nextval('public.marketing_material_id_seq'::regclass);


--
-- Name: marketing_material_partner id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner ALTER COLUMN id SET DEFAULT nextval('public.marketing_material_partner_id_seq'::regclass);


--
-- Name: obligation_check_section id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.obligation_check_section ALTER COLUMN id SET DEFAULT nextval('public.obligation_check_section_id_seq'::regclass);


--
-- Name: other_cost id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.other_cost ALTER COLUMN id SET DEFAULT nextval('public.other_cost_id_seq'::regclass);


--
-- Name: packaging_service id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service ALTER COLUMN id SET DEFAULT nextval('public.packaging_service_id_seq'::regclass);


--
-- Name: partner id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner ALTER COLUMN id SET DEFAULT nextval('public.partner_id_seq'::regclass);


--
-- Name: partner_banking id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_banking ALTER COLUMN id SET DEFAULT nextval('public.partner_banking_id_seq'::regclass);


--
-- Name: partner_contract id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract ALTER COLUMN id SET DEFAULT nextval('public.partner_contract_id_seq'::regclass);


--
-- Name: partner_contract_change id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract_change ALTER COLUMN id SET DEFAULT nextval('public.partner_contract_change_id_seq'::regclass);


--
-- Name: price_list id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.price_list ALTER COLUMN id SET DEFAULT nextval('public.price_list_id_seq'::regclass);


--
-- Name: recommended_country id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.recommended_country ALTER COLUMN id SET DEFAULT nextval('public.recommended_country_id_seq'::regclass);


--
-- Name: report_decline_reason id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_decline_reason ALTER COLUMN id SET DEFAULT nextval('public.report_decline_reason_id_seq'::regclass);


--
-- Name: report_set id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set ALTER COLUMN id SET DEFAULT nextval('public.report_set_id_seq'::regclass);


--
-- Name: report_set_column id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column ALTER COLUMN id SET DEFAULT nextval('public.report_set_column_id_seq'::regclass);


--
-- Name: report_set_column_fraction id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction ALTER COLUMN id SET DEFAULT nextval('public.report_set_column_fraction_id_seq'::regclass);


--
-- Name: report_set_fraction id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_fraction ALTER COLUMN id SET DEFAULT nextval('public.report_set_fraction_id_seq'::regclass);


--
-- Name: report_set_frequency id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_frequency ALTER COLUMN id SET DEFAULT nextval('public.report_set_frequency_id_seq'::regclass);


--
-- Name: report_set_price_list id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list ALTER COLUMN id SET DEFAULT nextval('public.report_set_price_list_id_seq'::regclass);


--
-- Name: report_set_price_list_item id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item ALTER COLUMN id SET DEFAULT nextval('public.report_set_price_list_item_id_seq'::regclass);


--
-- Name: representative_tier id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.representative_tier ALTER COLUMN id SET DEFAULT nextval('public.representative_tier_id_seq'::regclass);


--
-- Name: required_information id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information ALTER COLUMN id SET DEFAULT nextval('public.required_information_id_seq'::regclass);


--
-- Name: service_next_step id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.service_next_step ALTER COLUMN id SET DEFAULT nextval('public.service_next_step_id_seq'::regclass);


--
-- Name: settings id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.settings ALTER COLUMN id SET DEFAULT nextval('public.settings_id_seq'::regclass);


--
-- Name: shopping_cart_item id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item ALTER COLUMN id SET DEFAULT nextval('public.shopping_cart_item_id_seq'::regclass);


--
-- Name: termination id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination ALTER COLUMN id SET DEFAULT nextval('public.termination_id_seq'::regclass);


--
-- Name: termination_termination_reason id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_termination_reason ALTER COLUMN id SET DEFAULT nextval('public.termination_termination_reason_id_seq'::regclass);


--
-- Name: upload_file_history id; Type: DEFAULT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.upload_file_history ALTER COLUMN id SET DEFAULT nextval('public.upload_file_history_id_seq'::regclass);


--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
\.


--
-- Data for Name: action_guide; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.action_guide (id, contract_id, country_id, country_code, country_name, country_flag, created_at, updated_at, deleted_at, contract_status, termination_id) FROM stdin;
\.


--
-- Data for Name: action_guide_price_list; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.action_guide_price_list (id, setup_price_list_id, action_guide_id, name, description, price, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: broker; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.broker (id, name, email, phone, enroled_at, company_name, vat, tax, created_at, updated_at, deleted_at, is_active, user_id) FROM stdin;
\.


--
-- Data for Name: broker_company; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.broker_company (id, broker_id, name, register_number, vat, tax, country_code, address_number, address_street, city, contact_name, contact_email, phone_number, file_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: broker_company_order; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.broker_company_order (id, customer_number, company_id, transfer_date, order_number, year, fractions, status, created_at, deleted_at, updated_at, type, net_value, file_id) FROM stdin;
\.


--
-- Data for Name: certificate; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.certificate (id, license_id, name, status, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: change_user_email; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.change_user_email (id, new_email, token, user_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: cluster; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.cluster (id, name, registration_start_date, registration_end_date, status, min_household_packaging, max_household_packaging, type_of_services, participating_countries, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: cluster_customers; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.cluster_customers (id, cluster_id, customer_id) FROM stdin;
\.


--
-- Data for Name: commission; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.commission (id, user_id, user_type, commission_percentage, commission_value, net_turnover, type, coupon_id, coupon_code, affiliate_link, service_type, order_id, order_customer_id, created_at, used) FROM stdin;
\.


--
-- Data for Name: company; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.company (id, name, description, vat, tin, lucid, customer_id, starting, website, partner_id, address_id, created_at, updated_at, deleted_at, industry_sector, owner_name) FROM stdin;
\.


--
-- Data for Name: company_address; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.company_address (id, country_code, address_line, city, zip_code, street_and_number, additional_address, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: company_billing; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.company_billing (id, company_id, is_custom, full_name, country_code, country_name, company_name, street_and_number, city, zip_code, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: company_contact; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.company_contact (id, company_id, name, email, phone_mobile, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: company_email; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.company_email (id, email, company_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: consent; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.consent (id, name, type, description, version, created_at, updated_at, deleted_at) FROM stdin;
1	Data protection*	ACCOUNT	I here by accept the <a href="#">data protection regulations</a>	1	2024-12-13 19:05:54.997	2024-12-13 19:05:54.997	\N
2	Result by mail – we need you consent to send you e-mails*	ACCOUNT	Yes, I would like to subscribe to the Lizenzero Newsletter with important information about packaging regulations and promotions and would like to receive tailored information on the basis of my personalized user profile and the possibility to submit purchase reviews. If you do not wish to receive (further) advertising, please inform us of your wish, for example by telephone, by e-<NAME_EMAIL>, via menu item “My Account” in your customer account or click on the "Unsubscribe" link at the end of our e-mail.	1	2024-12-13 19:05:54.997	2024-12-13 19:05:54.997	\N
\.


--
-- Data for Name: contract; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.contract (id, customer_id, type, status, title, start_date, termination_id, created_at, updated_at, deleted_at, end_date) FROM stdin;
\.


--
-- Data for Name: country; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.country (id, name, created_at, updated_at, authorize_representative_obligated, code, flag_url, other_costs_obligated, is_published, license_required) FROM stdin;
31	Algeria	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	DZ	https://cdn.kcak11.com/CountryFlags/countries/dz.svg	f	f	f
32	Angola	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AO	https://cdn.kcak11.com/CountryFlags/countries/ao.svg	f	f	f
33	Benin	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BJ	https://cdn.kcak11.com/CountryFlags/countries/bj.svg	f	f	f
34	Botswana	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BW	https://cdn.kcak11.com/CountryFlags/countries/bw.svg	f	f	f
35	Burkina Faso	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BF	https://cdn.kcak11.com/CountryFlags/countries/bf.svg	f	f	f
36	Burundi	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BI	https://cdn.kcak11.com/CountryFlags/countries/bi.svg	f	f	f
37	Cameroon	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CM	https://cdn.kcak11.com/CountryFlags/countries/cm.svg	f	f	f
38	Cape Verde	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CV	https://cdn.kcak11.com/CountryFlags/countries/cv.svg	f	f	f
39	Central African Republic	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CF	https://cdn.kcak11.com/CountryFlags/countries/cf.svg	f	f	f
40	Chad	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TD	https://cdn.kcak11.com/CountryFlags/countries/td.svg	f	f	f
41	Comoros	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KM	https://cdn.kcak11.com/CountryFlags/countries/km.svg	f	f	f
42	Congo	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CG	https://cdn.kcak11.com/CountryFlags/countries/cg.svg	f	f	f
43	Democratic Republic of the Congo	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CD	https://cdn.kcak11.com/CountryFlags/countries/cd.svg	f	f	f
44	Djibouti	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	DJ	https://cdn.kcak11.com/CountryFlags/countries/dj.svg	f	f	f
45	Egypt	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	EG	https://cdn.kcak11.com/CountryFlags/countries/eg.svg	f	f	f
46	Equatorial Guinea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GQ	https://cdn.kcak11.com/CountryFlags/countries/gq.svg	f	f	f
47	Eritrea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ER	https://cdn.kcak11.com/CountryFlags/countries/er.svg	f	f	f
48	Eswatini	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SZ	https://cdn.kcak11.com/CountryFlags/countries/sz.svg	f	f	f
49	Ethiopia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ET	https://cdn.kcak11.com/CountryFlags/countries/et.svg	f	f	f
50	Gabon	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GA	https://cdn.kcak11.com/CountryFlags/countries/ga.svg	f	f	f
51	Gambia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GM	https://cdn.kcak11.com/CountryFlags/countries/gm.svg	f	f	f
52	Ghana	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GH	https://cdn.kcak11.com/CountryFlags/countries/gh.svg	f	f	f
53	Guinea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GN	https://cdn.kcak11.com/CountryFlags/countries/gn.svg	f	f	f
54	Guinea-Bissau	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GW	https://cdn.kcak11.com/CountryFlags/countries/gw.svg	f	f	f
55	Ivory Coast	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CI	https://cdn.kcak11.com/CountryFlags/countries/ci.svg	f	f	f
56	Kenya	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KE	https://cdn.kcak11.com/CountryFlags/countries/ke.svg	f	f	f
57	Lesotho	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LS	https://cdn.kcak11.com/CountryFlags/countries/ls.svg	f	f	f
58	Liberia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LR	https://cdn.kcak11.com/CountryFlags/countries/lr.svg	f	f	f
59	Libya	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LY	https://cdn.kcak11.com/CountryFlags/countries/ly.svg	f	f	f
60	Madagascar	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MG	https://cdn.kcak11.com/CountryFlags/countries/mg.svg	f	f	f
61	Malawi	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MW	https://cdn.kcak11.com/CountryFlags/countries/mw.svg	f	f	f
62	Mali	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ML	https://cdn.kcak11.com/CountryFlags/countries/ml.svg	f	f	f
63	Mauritania	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MR	https://cdn.kcak11.com/CountryFlags/countries/mr.svg	f	f	f
64	Mauritius	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MU	https://cdn.kcak11.com/CountryFlags/countries/mu.svg	f	f	f
65	Morocco	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MA	https://cdn.kcak11.com/CountryFlags/countries/ma.svg	f	f	f
66	Mozambique	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MZ	https://cdn.kcak11.com/CountryFlags/countries/mz.svg	f	f	f
67	Namibia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NA	https://cdn.kcak11.com/CountryFlags/countries/na.svg	f	f	f
68	Niger	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NE	https://cdn.kcak11.com/CountryFlags/countries/ne.svg	f	f	f
69	Nigeria	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NG	https://cdn.kcak11.com/CountryFlags/countries/ng.svg	f	f	f
70	Rwanda	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	RW	https://cdn.kcak11.com/CountryFlags/countries/rw.svg	f	f	f
71	Sao Tome and Principe	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ST	https://cdn.kcak11.com/CountryFlags/countries/st.svg	f	f	f
72	Senegal	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SN	https://cdn.kcak11.com/CountryFlags/countries/sn.svg	f	f	f
73	Seychelles	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SC	https://cdn.kcak11.com/CountryFlags/countries/sc.svg	f	f	f
74	Sierra Leone	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SL	https://cdn.kcak11.com/CountryFlags/countries/sl.svg	f	f	f
75	Somalia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SO	https://cdn.kcak11.com/CountryFlags/countries/so.svg	f	f	f
76	South Africa	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ZA	https://cdn.kcak11.com/CountryFlags/countries/za.svg	f	f	f
77	South Sudan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SS	https://cdn.kcak11.com/CountryFlags/countries/ss.svg	f	f	f
78	Sudan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SD	https://cdn.kcak11.com/CountryFlags/countries/sd.svg	f	f	f
79	Tanzania	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TZ	https://cdn.kcak11.com/CountryFlags/countries/tz.svg	f	f	f
80	Togo	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TG	https://cdn.kcak11.com/CountryFlags/countries/tg.svg	f	f	f
81	Tunisia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TN	https://cdn.kcak11.com/CountryFlags/countries/tn.svg	f	f	f
82	Uganda	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	UG	https://cdn.kcak11.com/CountryFlags/countries/ug.svg	f	f	f
83	Zambia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ZM	https://cdn.kcak11.com/CountryFlags/countries/zm.svg	f	f	f
84	Zimbabwe	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ZW	https://cdn.kcak11.com/CountryFlags/countries/zw.svg	f	f	f
85	Afghanistan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AF	https://cdn.kcak11.com/CountryFlags/countries/af.svg	f	f	f
86	Armenia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AM	https://cdn.kcak11.com/CountryFlags/countries/am.svg	f	f	f
87	Azerbaijan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AZ	https://cdn.kcak11.com/CountryFlags/countries/az.svg	f	f	f
88	Bahrain	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BH	https://cdn.kcak11.com/CountryFlags/countries/bh.svg	f	f	f
89	Bangladesh	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BD	https://cdn.kcak11.com/CountryFlags/countries/bd.svg	f	f	f
90	Bhutan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BT	https://cdn.kcak11.com/CountryFlags/countries/bt.svg	f	f	f
91	Brunei	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BN	https://cdn.kcak11.com/CountryFlags/countries/bn.svg	f	f	f
92	Cambodia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KH	https://cdn.kcak11.com/CountryFlags/countries/kh.svg	f	f	f
93	China	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CN	https://cdn.kcak11.com/CountryFlags/countries/cn.svg	f	f	f
94	Georgia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GE	https://cdn.kcak11.com/CountryFlags/countries/ge.svg	f	f	f
95	India	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	IN	https://cdn.kcak11.com/CountryFlags/countries/in.svg	f	f	f
96	Indonesia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ID	https://cdn.kcak11.com/CountryFlags/countries/id.svg	f	f	f
97	Iran	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	IR	https://cdn.kcak11.com/CountryFlags/countries/ir.svg	f	f	f
98	Iraq	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	IQ	https://cdn.kcak11.com/CountryFlags/countries/iq.svg	f	f	f
99	Israel	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	IL	https://cdn.kcak11.com/CountryFlags/countries/il.svg	f	f	f
100	Japan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	JP	https://cdn.kcak11.com/CountryFlags/countries/jp.svg	f	f	f
101	Jordan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	JO	https://cdn.kcak11.com/CountryFlags/countries/jo.svg	f	f	f
102	Kazakhstan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KZ	https://cdn.kcak11.com/CountryFlags/countries/kz.svg	f	f	f
103	Kuwait	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KW	https://cdn.kcak11.com/CountryFlags/countries/kw.svg	f	f	f
104	Kyrgyzstan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KG	https://cdn.kcak11.com/CountryFlags/countries/kg.svg	f	f	f
105	Laos	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LA	https://cdn.kcak11.com/CountryFlags/countries/la.svg	f	f	f
106	Lebanon	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LB	https://cdn.kcak11.com/CountryFlags/countries/lb.svg	f	f	f
107	Malaysia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MY	https://cdn.kcak11.com/CountryFlags/countries/my.svg	f	f	f
108	Maldives	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MV	https://cdn.kcak11.com/CountryFlags/countries/mv.svg	f	f	f
109	Mongolia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MN	https://cdn.kcak11.com/CountryFlags/countries/mn.svg	f	f	f
110	Myanmar	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MM	https://cdn.kcak11.com/CountryFlags/countries/mm.svg	f	f	f
111	Nepal	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NP	https://cdn.kcak11.com/CountryFlags/countries/np.svg	f	f	f
112	North Korea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KP	https://cdn.kcak11.com/CountryFlags/countries/kp.svg	f	f	f
113	Oman	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	OM	https://cdn.kcak11.com/CountryFlags/countries/om.svg	f	f	f
114	Pakistan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PK	https://cdn.kcak11.com/CountryFlags/countries/pk.svg	f	f	f
115	Palestine	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PS	https://cdn.kcak11.com/CountryFlags/countries/ps.svg	f	f	f
116	Philippines	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PH	https://cdn.kcak11.com/CountryFlags/countries/ph.svg	f	f	f
117	Qatar	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	QA	https://cdn.kcak11.com/CountryFlags/countries/qa.svg	f	f	f
118	Saudi Arabia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SA	https://cdn.kcak11.com/CountryFlags/countries/sa.svg	f	f	f
119	Singapore	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SG	https://cdn.kcak11.com/CountryFlags/countries/sg.svg	f	f	f
120	South Korea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KR	https://cdn.kcak11.com/CountryFlags/countries/kr.svg	f	f	f
121	Sri Lanka	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LK	https://cdn.kcak11.com/CountryFlags/countries/lk.svg	f	f	f
122	Syria	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SY	https://cdn.kcak11.com/CountryFlags/countries/sy.svg	f	f	f
123	Taiwan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TW	https://cdn.kcak11.com/CountryFlags/countries/tw.svg	f	f	f
124	Tajikistan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TJ	https://cdn.kcak11.com/CountryFlags/countries/tj.svg	f	f	f
125	Thailand	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TH	https://cdn.kcak11.com/CountryFlags/countries/th.svg	f	f	f
126	Turkey	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TR	https://cdn.kcak11.com/CountryFlags/countries/tr.svg	f	f	f
127	Turkmenistan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TM	https://cdn.kcak11.com/CountryFlags/countries/tm.svg	f	f	f
128	United Arab Emirates	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AE	https://cdn.kcak11.com/CountryFlags/countries/ae.svg	f	f	f
129	Uzbekistan	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	UZ	https://cdn.kcak11.com/CountryFlags/countries/uz.svg	f	f	f
130	Vietnam	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	VN	https://cdn.kcak11.com/CountryFlags/countries/vn.svg	f	f	f
131	Yemen	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	YE	https://cdn.kcak11.com/CountryFlags/countries/ye.svg	f	f	f
132	Albania	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AL	https://cdn.kcak11.com/CountryFlags/countries/al.svg	f	f	f
133	Andorra	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AD	https://cdn.kcak11.com/CountryFlags/countries/ad.svg	f	f	f
134	Belarus	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BY	https://cdn.kcak11.com/CountryFlags/countries/by.svg	f	f	f
135	Bosnia and Herzegovina	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BA	https://cdn.kcak11.com/CountryFlags/countries/ba.svg	f	f	f
136	Iceland	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	IS	https://cdn.kcak11.com/CountryFlags/countries/is.svg	f	f	f
137	Kosovo	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	XK	https://cdn.kcak11.com/CountryFlags/countries/xk.svg	f	f	f
138	Liechtenstein	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LI	https://cdn.kcak11.com/CountryFlags/countries/li.svg	f	f	f
139	Moldova	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MD	https://cdn.kcak11.com/CountryFlags/countries/md.svg	f	f	f
140	Monaco	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MC	https://cdn.kcak11.com/CountryFlags/countries/mc.svg	f	f	f
141	Montenegro	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	ME	https://cdn.kcak11.com/CountryFlags/countries/me.svg	f	f	f
142	North Macedonia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MK	https://cdn.kcak11.com/CountryFlags/countries/mk.svg	f	f	f
143	Russia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	RU	https://cdn.kcak11.com/CountryFlags/countries/ru.svg	f	f	f
144	San Marino	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SM	https://cdn.kcak11.com/CountryFlags/countries/sm.svg	f	f	f
145	Serbia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	RS	https://cdn.kcak11.com/CountryFlags/countries/rs.svg	f	f	f
146	Ukraine	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	UA	https://cdn.kcak11.com/CountryFlags/countries/ua.svg	f	f	f
147	Vatican City	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	VA	https://cdn.kcak11.com/CountryFlags/countries/va.svg	f	f	f
148	Antigua and Barbuda	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AG	https://cdn.kcak11.com/CountryFlags/countries/ag.svg	f	f	f
149	Bahamas	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BS	https://cdn.kcak11.com/CountryFlags/countries/bs.svg	f	f	f
150	Barbados	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BB	https://cdn.kcak11.com/CountryFlags/countries/bb.svg	f	f	f
151	Belize	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BZ	https://cdn.kcak11.com/CountryFlags/countries/bz.svg	f	f	f
152	Canada	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CA	https://cdn.kcak11.com/CountryFlags/countries/ca.svg	f	f	f
153	Costa Rica	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CR	https://cdn.kcak11.com/CountryFlags/countries/cr.svg	f	f	f
154	Cuba	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CU	https://cdn.kcak11.com/CountryFlags/countries/cu.svg	f	f	f
155	Dominica	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	DM	https://cdn.kcak11.com/CountryFlags/countries/dm.svg	f	f	f
156	Dominican Republic	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	DO	https://cdn.kcak11.com/CountryFlags/countries/do.svg	f	f	f
157	El Salvador	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SV	https://cdn.kcak11.com/CountryFlags/countries/sv.svg	f	f	f
158	Grenada	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GD	https://cdn.kcak11.com/CountryFlags/countries/gd.svg	f	f	f
159	Guatemala	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GT	https://cdn.kcak11.com/CountryFlags/countries/gt.svg	f	f	f
160	Haiti	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	HT	https://cdn.kcak11.com/CountryFlags/countries/ht.svg	f	f	f
161	Honduras	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	HN	https://cdn.kcak11.com/CountryFlags/countries/hn.svg	f	f	f
162	Jamaica	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	JM	https://cdn.kcak11.com/CountryFlags/countries/jm.svg	f	f	f
163	Mexico	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MX	https://cdn.kcak11.com/CountryFlags/countries/mx.svg	f	f	f
164	Nicaragua	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NI	https://cdn.kcak11.com/CountryFlags/countries/ni.svg	f	f	f
165	Panama	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PA	https://cdn.kcak11.com/CountryFlags/countries/pa.svg	f	f	f
166	Saint Kitts and Nevis	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KN	https://cdn.kcak11.com/CountryFlags/countries/kn.svg	f	f	f
167	Saint Lucia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	LC	https://cdn.kcak11.com/CountryFlags/countries/lc.svg	f	f	f
168	Saint Vincent and the Grenadines	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	VC	https://cdn.kcak11.com/CountryFlags/countries/vc.svg	f	f	f
169	Trinidad and Tobago	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TT	https://cdn.kcak11.com/CountryFlags/countries/tt.svg	f	f	f
170	United States	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	US	https://cdn.kcak11.com/CountryFlags/countries/us.svg	f	f	f
171	Argentina	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AR	https://cdn.kcak11.com/CountryFlags/countries/ar.svg	f	f	f
172	Bolivia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BO	https://cdn.kcak11.com/CountryFlags/countries/bo.svg	f	f	f
173	Brazil	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	BR	https://cdn.kcak11.com/CountryFlags/countries/br.svg	f	f	f
174	Chile	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CL	https://cdn.kcak11.com/CountryFlags/countries/cl.svg	f	f	f
175	Colombia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	CO	https://cdn.kcak11.com/CountryFlags/countries/co.svg	f	f	f
176	Ecuador	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	EC	https://cdn.kcak11.com/CountryFlags/countries/ec.svg	f	f	f
177	Guyana	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	GY	https://cdn.kcak11.com/CountryFlags/countries/gy.svg	f	f	f
178	Paraguay	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PY	https://cdn.kcak11.com/CountryFlags/countries/py.svg	f	f	f
179	Peru	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PE	https://cdn.kcak11.com/CountryFlags/countries/pe.svg	f	f	f
180	Suriname	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SR	https://cdn.kcak11.com/CountryFlags/countries/sr.svg	f	f	f
181	Uruguay	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	UY	https://cdn.kcak11.com/CountryFlags/countries/uy.svg	f	f	f
182	Venezuela	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	VE	https://cdn.kcak11.com/CountryFlags/countries/ve.svg	f	f	f
183	Australia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	AU	https://cdn.kcak11.com/CountryFlags/countries/au.svg	f	f	f
184	Fiji	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	FJ	https://cdn.kcak11.com/CountryFlags/countries/fj.svg	f	f	f
185	Kiribati	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	KI	https://cdn.kcak11.com/CountryFlags/countries/ki.svg	f	f	f
186	Marshall Islands	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	MH	https://cdn.kcak11.com/CountryFlags/countries/mh.svg	f	f	f
187	Micronesia	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	FM	https://cdn.kcak11.com/CountryFlags/countries/fm.svg	f	f	f
188	Nauru	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NR	https://cdn.kcak11.com/CountryFlags/countries/nr.svg	f	f	f
189	New Zealand	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	NZ	https://cdn.kcak11.com/CountryFlags/countries/nz.svg	f	f	f
190	Palau	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PW	https://cdn.kcak11.com/CountryFlags/countries/pw.svg	f	f	f
191	Papua New Guinea	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	PG	https://cdn.kcak11.com/CountryFlags/countries/pg.svg	f	f	f
192	Samoa	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	WS	https://cdn.kcak11.com/CountryFlags/countries/ws.svg	f	f	f
193	Solomon Islands	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	SB	https://cdn.kcak11.com/CountryFlags/countries/sb.svg	f	f	f
194	Tonga	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TO	https://cdn.kcak11.com/CountryFlags/countries/to.svg	f	f	f
195	Tuvalu	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	TV	https://cdn.kcak11.com/CountryFlags/countries/tv.svg	f	f	f
196	Vanuatu	2025-09-02 08:41:54.907	2025-09-02 08:41:54.907	f	VU	https://cdn.kcak11.com/CountryFlags/countries/vu.svg	f	f	f
2	Belgium	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	BE	https://cdn.kcak11.com/CountryFlags/countries/be.svg	f	f	t
3	Bulgaria	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	BG	https://cdn.kcak11.com/CountryFlags/countries/bg.svg	f	f	t
4	Croatia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	HR	https://cdn.kcak11.com/CountryFlags/countries/hr.svg	f	f	t
5	Cyprus	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	CY	https://cdn.kcak11.com/CountryFlags/countries/cy.svg	f	f	t
6	Czechia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	CZ	https://cdn.kcak11.com/CountryFlags/countries/cz.svg	f	f	t
7	Denmark	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	DK	https://cdn.kcak11.com/CountryFlags/countries/dk.svg	f	f	t
8	Estonia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	EE	https://cdn.kcak11.com/CountryFlags/countries/ee.svg	f	f	t
9	Finland	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	FI	https://cdn.kcak11.com/CountryFlags/countries/fi.svg	f	f	t
10	France	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	FR	https://cdn.kcak11.com/CountryFlags/countries/fr.svg	f	f	t
11	Germany	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	DE	https://cdn.kcak11.com/CountryFlags/countries/de.svg	f	f	t
12	Greece	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	GR	https://cdn.kcak11.com/CountryFlags/countries/gr.svg	f	f	t
13	Hungary	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	HU	https://cdn.kcak11.com/CountryFlags/countries/hu.svg	f	f	t
14	Ireland	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	IE	https://cdn.kcak11.com/CountryFlags/countries/ie.svg	f	f	t
15	Italy	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	IT	https://cdn.kcak11.com/CountryFlags/countries/it.svg	f	f	t
16	Latvia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	LV	https://cdn.kcak11.com/CountryFlags/countries/lv.svg	f	f	t
17	Lithuania	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	LT	https://cdn.kcak11.com/CountryFlags/countries/lt.svg	f	f	t
18	Luxembourg	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	LU	https://cdn.kcak11.com/CountryFlags/countries/lu.svg	f	f	t
19	Malta	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	MT	https://cdn.kcak11.com/CountryFlags/countries/mt.svg	f	f	t
20	Netherlands	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	NL	https://cdn.kcak11.com/CountryFlags/countries/nl.svg	f	f	t
21	Poland	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	PL	https://cdn.kcak11.com/CountryFlags/countries/pl.svg	f	f	t
22	Portugal	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	PT	https://cdn.kcak11.com/CountryFlags/countries/pt.svg	f	f	t
23	Romania	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	RO	https://cdn.kcak11.com/CountryFlags/countries/ro.svg	f	f	t
24	Slovakia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	SK	https://cdn.kcak11.com/CountryFlags/countries/sk.svg	f	f	t
25	Slovenia	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	SI	https://cdn.kcak11.com/CountryFlags/countries/si.svg	f	f	t
26	Spain	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	ES	https://cdn.kcak11.com/CountryFlags/countries/es.svg	f	f	t
27	Sweden	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	SE	https://cdn.kcak11.com/CountryFlags/countries/se.svg	f	f	t
28	United Kingdom	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	GB	https://cdn.kcak11.com/CountryFlags/countries/gb.svg	f	f	t
29	Norway	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	NO	https://cdn.kcak11.com/CountryFlags/countries/no.svg	f	f	t
30	Switzerland	2025-09-02 08:41:54.866	2025-09-02 08:41:54.866	f	CH	https://cdn.kcak11.com/CountryFlags/countries/ch.svg	f	f	t
1	Austria	2025-09-02 08:41:54.866	2025-09-02 12:22:56.52	f	AT	https://cdn.kcak11.com/CountryFlags/countries/at.svg	f	f	t
\.


--
-- Data for Name: country_follower; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.country_follower (id, country_id, user_id, user_email, user_first_name, user_last_name, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: country_price_list; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.country_price_list (id, country_id, price_list_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: coupon; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.coupon (id, buy_x_get_y, code, commission_percentage, description, discount_type, elegible_products, end_date, is_active, link, max_amount, max_uses, max_uses_per_customer, min_amount, min_products, mode, note, start_date, type, value, created_at, updated_at, deleted_at, redeemable_by_new_customers, for_commission, used_at) FROM stdin;
\.


--
-- Data for Name: coupon_customer; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.coupon_customer (id, coupon_id, customer_id) FROM stdin;
\.


--
-- Data for Name: coupon_partners; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.coupon_partners (id, coupon_id, partner_id) FROM stdin;
\.


--
-- Data for Name: coupon_uses; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.coupon_uses (id, coupon_id, shopping_cart_id, order_id, customer_id, created_at, is_first_purchase) FROM stdin;
\.


--
-- Data for Name: criteria; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.criteria (id, created_at, deleted_at, help_text, input_type, mode, title, type, updated_at, country_id, packaging_service_id, required_information_id, calculator_type, obligation_check_section_id) FROM stdin;
1	2025-09-02 08:50:08.121	\N	Select yes will get 2 service types	YES_NO	COMMITMENT	Select service type	PACKAGING_SERVICE	2025-09-02 08:50:08.147	1	1	\N	\N	1
2	2025-09-02 08:55:36.324	\N		YES_NO	COMMITMENT	question to select fraction set	REPORT_SET	2025-09-02 08:55:36.33	1	1	\N	\N	\N
3	2025-09-02 08:57:21.518	\N	yes is a, no is b	YES_NO	COMMITMENT	Select fraction set	REPORT_SET	2025-09-02 08:57:34.118	1	2	\N	\N	\N
4	2025-09-02 08:58:18.722	\N	Report Rhythm	YES_NO	COMMITMENT	Select Report Rhythm	REPORT_FREQUENCY	2025-09-02 08:58:18.728	1	1	\N	\N	\N
6	2025-09-02 09:00:08.096	\N	\N	SELECT	COMMITMENT	select cost 2 or 3	OTHER_COST	2025-09-02 09:00:08.116	1	\N	\N	\N	\N
5	2025-09-02 08:59:21.921	\N	\N	YES_NO	COMMITMENT	select cost 1	OTHER_COST	2025-09-02 09:00:08.174	1	\N	\N	\N	\N
7	2025-09-02 09:01:40.583	\N		YES_NO	COMMITMENT	required Information for sales packaging?	REQUIRED_INFORMATION	2025-09-02 09:01:40.59	1	\N	1	\N	\N
\.


--
-- Data for Name: criteria_option; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.criteria_option (id, criteria_id, value, created_at, updated_at, deleted_at, option_value, option_to_value, optional_criteria_id) FROM stdin;
1	1	OBLIGED	2025-09-02 08:50:08.141	2025-09-02 08:50:08.141	\N	YES	\N	\N
2	1	NOT_OBLIGED	2025-09-02 08:50:08.143	2025-09-02 08:50:08.143	\N	NO	\N	\N
3	2	1	2025-09-02 08:55:36.328	2025-09-02 08:55:36.328	\N	YES	\N	\N
4	2	2	2025-09-02 08:55:36.328	2025-09-02 08:55:36.328	\N	NO	\N	\N
7	3	3	2025-09-02 08:57:34.115	2025-09-02 08:57:34.115	\N	YES	\N	\N
8	3	4	2025-09-02 08:57:34.116	2025-09-02 08:57:34.116	\N	NO	\N	\N
9	4	2	2025-09-02 08:58:18.726	2025-09-02 08:58:18.726	\N	YES	\N	\N
10	4	1	2025-09-02 08:58:18.726	2025-09-02 08:58:18.726	\N	NO	\N	\N
13	6	1	2025-09-02 09:00:08.099	2025-09-02 09:00:08.099	\N	I select cost 2	\N	\N
14	6	3	2025-09-02 09:00:08.101	2025-09-02 09:00:08.101	\N	I select cost 3	\N	\N
15	5	2	2025-09-02 09:00:08.168	2025-09-02 09:00:08.168	\N	YES	\N	\N
16	5	no_cost	2025-09-02 09:00:08.17	2025-09-02 09:00:08.17	\N	NO	\N	\N
17	7	REQUEST	2025-09-02 09:01:40.586	2025-09-02 09:01:40.586	\N	YES	\N	\N
18	7	NOT_REQUEST	2025-09-02 09:01:40.587	2025-09-02 09:01:40.587	\N	NO	\N	\N
\.


--
-- Data for Name: customer; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer (id, type, first_name, last_name, salutation, email, user_id, is_active, document_id, id_stripe, created_at, updated_at, deleted_at, company_name, language, currency) FROM stdin;
\.


--
-- Data for Name: customer_activities; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_activities (id, type, metadata, created_at, updated_at, deleted_at, customer_id) FROM stdin;
\.


--
-- Data for Name: customer_commitment; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_commitment (id, customer_email, country_code, year, commitment, service_setup, is_license_required, blame, created_at, updated_at, deleted_at, shopping_cart_id) FROM stdin;
\.


--
-- Data for Name: customer_consent; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_consent (id, customer_id, consent_id, given, "givenAt", "revokedAt", created_at, updated_at) FROM stdin;
\.


--
-- Data for Name: customer_document; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_document (id, customer_id, document_url, status) FROM stdin;
\.


--
-- Data for Name: customer_invitation; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_invitation (id, comission_date, product, comission, order_number, lead_source, customer_id, invited_customer_id) FROM stdin;
\.


--
-- Data for Name: customer_invite_token; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_invite_token (id, token, share_link, customer_id, created_at, updated_at, deleted_at, expiration_date) FROM stdin;
\.


--
-- Data for Name: customer_phone; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_phone (id, phone_number, customer_id, created_at, updated_at, deleted_at, phone_type) FROM stdin;
\.


--
-- Data for Name: customer_tutorial; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.customer_tutorial (id, customer_id, service_type, is_finished, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: decline; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.decline (id, title, license_required_information_id, license_volume_report_id, license_volume_report_item_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: decline_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.decline_reason (id, created_at, updated_at, deleted_at, decline_id, reason_id) FROM stdin;
\.


--
-- Data for Name: decline_to_decline_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.decline_to_decline_reason (id, decline_id, reason_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: file; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.file (id, user_id, name, original_name, extension, size, type, created_at, updated_at, deleted_at, required_information_id, contract_id, certificate_id, license_id, termination_id, general_information_id, third_party_invoice_id, marketing_material_id, partner_contract_id, order_id) FROM stdin;
\.


--
-- Data for Name: files; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.files (id, name, extension, size, creator_type, document_type, user_id, created_at, updated_at, original_name, country_id) FROM stdin;
7b0e97ff-427a-4355-b3ab-0f7c64d94a0a	FRACTION_ICON/2025/09/02/R20FJTH1-plastic.png	image/png	1797	ADMIN	FRACTION_ICON	0	2025-09-02 08:51:17.97	2025-09-02 08:51:17.97	plastic.png	1
\.


--
-- Data for Name: flyway_schema_history; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.flyway_schema_history (installed_rank, version, description, type, script, checksum, installed_by, installed_on, execution_time, success) FROM stdin;
1	0.0.1	customer entities	SQL	V0.0.1__customer_entities.sql	-993860634	oneepr-local-user	2025-09-02 08:41:54.118087	209	t
2	0.0.2	admin entities	SQL	V0.0.2__admin_entities.sql	-1257153119	oneepr-local-user	2025-09-02 08:41:54.535145	83	t
3	0.0.3	auth entities	SQL	V0.0.3__auth_entities.sql	1245103010	oneepr-local-user	2025-09-02 08:41:54.772793	14	t
4	1.0.0	seed	SQL	V1.0.0__seed.sql	-1977346113	oneepr-local-user	2025-09-02 08:41:54.830788	5	t
5	1.0.1	country seed	SQL	V1.0.1__country_seed.sql	-330404864	oneepr-local-user	2025-09-02 08:41:54.862557	1	t
6	1.0.257	Create obligation check section table	SQL	V1.0.257__Create_obligation_check_section_table.sql	-563504226	oneepr-local-user	2025-09-02 08:41:54.868679	6	t
7	1.0.257.1	Create obligation check section table	SQL	V1.0.257.1__Create_obligation_check_section_table.sql	-1135431766	oneepr-local-user	2025-09-02 08:41:54.879344	3	t
8	1.0.258	Create required information packaging service relationship table	SQL	V1.0.258__Create_required_information_packaging_service_relationship_table.sql	1907407535	oneepr-local-user	2025-09-02 08:41:54.887126	5	t
9	1.0.259	world countries insert	SQL	V1.0.259__world_countries_insert.sql	-1370782669	oneepr-local-user	2025-09-02 08:41:54.897211	5	t
10	1.0.376	consent seed	SQL	V1.0.376__consent_seed.sql	1910269975	oneepr-local-user	2025-09-02 08:41:54.913536	6	t
11	1.0.376.1	settings seed	SQL	V1.0.376.1__settings_seed.sql	-1223354168	oneepr-local-user	2025-09-02 08:41:54.937519	2	t
\.


--
-- Data for Name: fraction_icon; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.fraction_icon (id, file_id, image_url, created_at, updated_at, deleted_at) FROM stdin;
1	7b0e97ff-427a-4355-b3ab-0f7c64d94a0a	FRACTION_ICON/2025/09/02/R20FJTH1-plastic.png	2025-09-02 08:51:18.335	2025-09-02 08:51:18.335	\N
\.


--
-- Data for Name: general_information; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.general_information (id, setup_general_information_id, contract_id, type, status, name, description, created_at, updated_at, deleted_at, question, file_id, answer) FROM stdin;
\.


--
-- Data for Name: license; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license (id, contract_id, registration_number, registration_status, clerk_control_status, contract_status, country_id, country_code, country_name, country_flag, year, start_date, end_date, termination_id, created_at, updated_at, deleted_at, registration_and_termination_monday_ref) FROM stdin;
\.


--
-- Data for Name: license_other_cost; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_other_cost (id, setup_other_cost_id, license_id, name, price, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_packaging_service; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_packaging_service (id, setup_packaging_service_id, license_id, name, description, created_at, updated_at, deleted_at, obliged) FROM stdin;
\.


--
-- Data for Name: license_price_list; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_price_list (id, setup_price_list_id, license_id, name, description, condition_type, condition_type_value, start_date, end_date, basic_price, minimum_price, registration_fee, handling_fee, variable_handling_fee, thresholds, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_report_set; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_report_set (id, setup_report_set_id, license_packaging_service_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_report_set_frequency; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_report_set_frequency (id, setup_report_set_frequency_id, license_packaging_service_id, rhythm, frequency, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_representative_tier; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_representative_tier (id, setup_representative_tier_id, license_id, name, price, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_required_information; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_required_information (id, setup_required_information_id, license_id, type, status, name, description, created_at, updated_at, deleted_at, question, file_id, answer, kind, contract_id, setup_general_information_id) FROM stdin;
\.


--
-- Data for Name: license_third_party_invoice; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_third_party_invoice (id, title, price, issued_at, status, license_id, created_at, updated_at, deleted_at, due_date, issuer, third_party_invoice_monday_ref) FROM stdin;
\.


--
-- Data for Name: license_volume_report; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_volume_report (id, license_packaging_service_id, status, year, "interval", report_table, created_at, updated_at, deleted_at, volume_report_monday_ref, stage) FROM stdin;
\.


--
-- Data for Name: license_volume_report_decline_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_volume_report_decline_reason (id, license_volume_report_error_id, report_decline_reason_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_volume_report_error; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_volume_report_error (id, license_volume_report_id, license_volume_report_item_id, description, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: license_volume_report_item; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.license_volume_report_item (id, license_volume_report_id, setup_fraction_id, setup_column_id, value, price, created_at, updated_at, deleted_at, setup_column_code, setup_fraction_code) FROM stdin;
\.


--
-- Data for Name: marketing_material; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.marketing_material (id, name, start_date, end_date, category, partner_restriction, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: marketing_material_partner; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.marketing_material_partner (id, marketing_material_id, partner_id) FROM stdin;
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.notifications (id, user_id, service, subject, message, type, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: obligation_check_section; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.obligation_check_section (id, title, display_order, country_id, created_at, updated_at) FROM stdin;
1	Section 1	1	1	2025-09-02 08:48:06.727013+00	2025-09-02 08:48:06.727023+00
\.


--
-- Data for Name: one_epr_role; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.one_epr_role (id, name, display_name, is_active, created_at, updated_at, deleted_at) FROM stdin;
5	SUPER_ADMIN	Super Administrator	t	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
9	ADMIN	Administrator	t	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
3	CLERK	Clerk	t	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
1	CUSTOMER	Customer	t	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
\.


--
-- Data for Name: one_epr_user; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.one_epr_user (id, first_name, last_name, name, email, password, is_active, role_id, token_verify, token_expiration, token_magic_link, token_create_password, token_attempts, block_time, status, created_at, updated_at, deleted_at) FROM stdin;
5	Super	Admin	superadmin	<EMAIL>	$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi	t	5	\N	\N	\N	\N	0	\N	COMPLETE	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
9	Admin	User	admin	<EMAIL>	$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi	t	9	\N	\N	\N	\N	0	\N	COMPLETE	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
3	Clerk	User	clerk	<EMAIL>	$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi	t	3	\N	\N	\N	\N	0	\N	COMPLETE	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
1	Customer	User	customer	<EMAIL>	$2a$10$hJJzOfhFS8OnObePdHJ88OThEmUngZCURydMdFSkdw1VzQwbkkNqi	t	1	\N	\N	\N	\N	0	\N	COMPLETE	2025-09-02 08:41:54.855617	2025-09-02 08:41:54.855617	\N
\.


--
-- Data for Name: other_cost; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.other_cost (id, name, price, country_id, created_at, updated_at, deleted_at) FROM stdin;
2	cost 1	10000	1	2025-09-02 08:58:46.606	2025-09-02 08:58:46.606	\N
1	cost 2	20000	1	2025-09-02 08:58:46.606	2025-09-02 08:58:46.606	\N
3	cost3	3000	1	2025-09-02 08:58:56.055	2025-09-02 08:58:56.055	\N
\.


--
-- Data for Name: packaging_service; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.packaging_service (id, name, description, country_id, created_at, updated_at, deleted_at) FROM stdin;
1	Sales packaging	Sales packaging	1	2025-09-02 08:48:42.781	2025-09-02 08:48:42.781	\N
2	Transport packaging	Transport packaging	1	2025-09-02 08:48:49.102	2025-09-02 08:48:49.102	\N
\.


--
-- Data for Name: packaging_service_criteria_option; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.packaging_service_criteria_option (packaging_service_id, criteria_option_id) FROM stdin;
2	1
1	1
\.


--
-- Data for Name: partner; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.partner (id, first_name, last_name, email, user_id, created_at, updated_at, deleted_at, commission_mode, commission_percentage, checked, no_provision_negotiated, payout_cycle, status) FROM stdin;
\.


--
-- Data for Name: partner_banking; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.partner_banking (id, partner_id, business_identifier_code, international_account_number, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: partner_contract; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.partner_contract (id, partner_id, status, agreed_on, start_date, end_date, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: partner_contract_change; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.partner_contract_change (id, partner_contract_id, change_type, change_description, created_at) FROM stdin;
\.


--
-- Data for Name: password_reset_request; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.password_reset_request (id, email, user_id, token, status, callback_url, expires_at) FROM stdin;
\.


--
-- Data for Name: price_list; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.price_list (id, type, name, description, start_date, end_date, basic_price, minimum_price, registration_fee, variable_handling_fee, price, created_at, updated_at, deleted_at, condition_type, condition_type_value, handling_fee, thresholds) FROM stdin;
\.


--
-- Data for Name: reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.reason (id, title, value, type, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: recommended_country; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.recommended_country (id, name, customer_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: refresh_token; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.refresh_token (id, user_id, token, created_at) FROM stdin;
\.


--
-- Data for Name: report_decline_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_decline_reason (id, title, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: report_set; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set (id, name, mode, type, created_at, updated_at, deleted_at, packaging_service_id, sheet_file_description, sheet_file_id) FROM stdin;
1	Sales packaging A	ON_PLATAFORM	FRACTIONS	2025-09-02 08:50:17.306	2025-09-02 08:52:47.062	\N	1	\N	\N
2	Sales packaging B	ON_PLATAFORM	FRACTIONS	2025-09-02 08:53:08.239	2025-09-02 08:53:45.987	\N	1	\N	\N
3	Transport packaging A	ON_PLATAFORM	FRACTIONS	2025-09-02 08:55:41.699	2025-09-02 08:56:39.065	\N	2	\N	\N
4	Transport packaging B	ON_PLATAFORM	FRACTIONS	2025-09-02 08:56:54.33	2025-09-02 08:57:01.93	\N	2	\N	\N
\.


--
-- Data for Name: report_set_column; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_column (id, name, description, unit_type, report_set_id, parent_id, created_at, updated_at, deleted_at, code, level, "order", parent_code) FROM stdin;
1	col name		KG	1	\N	2025-09-02 08:52:47.054	2025-09-02 08:52:47.054	\N	9LURY1	1	1	\N
2	sub col 1		KG	1	\N	2025-09-02 08:52:47.056	2025-09-02 08:52:47.056	\N	ZL8BME	2	1	9LURY1
5	col name		KG	2	\N	2025-09-02 08:53:45.98	2025-09-02 08:53:45.98	\N	Z9H7U0	1	1	\N
6	sub col 1		KG	2	\N	2025-09-02 08:53:45.981	2025-09-02 08:53:45.981	\N	VABUXN	2	1	Z9H7U0
7	col name		KG	3	\N	2025-09-02 08:56:39.054	2025-09-02 08:56:39.054	\N	6KGEIW	1	1	\N
8	sub col 1		KG	3	\N	2025-09-02 08:56:39.058	2025-09-02 08:56:39.058	\N	FQSNWF	2	1	6KGEIW
11	col name		KG	4	\N	2025-09-02 08:57:01.927	2025-09-02 08:57:01.927	\N	YJ7LIG	1	1	\N
12	sub col 1		KG	4	\N	2025-09-02 08:57:01.928	2025-09-02 08:57:01.928	\N	QEAMM6	2	1	YJ7LIG
\.


--
-- Data for Name: report_set_column_fraction; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_column_fraction (id, created_at, updated_at, deleted_at, column_code, fraction_code) FROM stdin;
1	2025-09-02 08:52:47.068	2025-09-02 08:52:47.068	\N	ZL8BME	47EP6R
2	2025-09-02 08:52:47.073	2025-09-02 08:52:47.073	\N	ZL8BME	8IIO4O
5	2025-09-02 08:53:45.994	2025-09-02 08:53:45.994	\N	VABUXN	IHB4FC
6	2025-09-02 08:53:45.997	2025-09-02 08:53:45.997	\N	VABUXN	V0Q03L
\.


--
-- Data for Name: report_set_fraction; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_fraction (id, name, description, report_set_id, parent_id, created_at, updated_at, deleted_at, icon, is_active, code, fraction_icon_id, level, "order", parent_code, has_second_level, has_third_level) FROM stdin;
1	Plastic	Plastic	1	\N	2025-09-02 08:52:47.029	2025-09-02 08:52:47.029	\N	aluminium	t	603T6C	1	1	1	\N	t	t
2	PE	PE	1	\N	2025-09-02 08:52:47.036	2025-09-02 08:52:47.036	\N	aluminium	t	PTLJGU	1	2	1	603T6C	f	f
3	PE-A	PE-A	1	\N	2025-09-02 08:52:47.038	2025-09-02 08:52:47.038	\N	aluminium	t	47EP6R	1	3	1	PTLJGU	f	f
4	Metals	Metals	1	\N	2025-09-02 08:52:47.039	2025-09-02 08:52:47.039	\N	aluminium	t	MR822Y	1	1	2	\N	t	t
5	Iron		1	\N	2025-09-02 08:52:47.04	2025-09-02 08:52:47.04	\N	aluminium	t	TQQNIP	1	2	1	MR822Y	f	f
6	Iron-A		1	\N	2025-09-02 08:52:47.046	2025-09-02 08:52:47.046	\N	aluminium	t	8IIO4O	1	3	1	TQQNIP	f	f
13	Plastic	Plastic	2	\N	2025-09-02 08:53:45.965	2025-09-02 08:53:45.965	\N	aluminium	t	VUCNC5	1	1	1	\N	t	t
14	PEE	PE	2	\N	2025-09-02 08:53:45.971	2025-09-02 08:53:45.971	\N	aluminium	t	TV3LYH	1	2	1	VUCNC5	f	f
15	PE-A	PE-A	2	\N	2025-09-02 08:53:45.973	2025-09-02 08:53:45.973	\N	aluminium	t	IHB4FC	1	3	1	TV3LYH	f	f
16	Metals	Metals	2	\N	2025-09-02 08:53:45.974	2025-09-02 08:53:45.974	\N	aluminium	t	H5NZE1	1	1	2	\N	t	t
17	Iron		2	\N	2025-09-02 08:53:45.975	2025-09-02 08:53:45.975	\N	aluminium	t	LPB7T8	1	2	1	H5NZE1	f	f
18	Iron-A		2	\N	2025-09-02 08:53:45.976	2025-09-02 08:53:45.976	\N	aluminium	t	V0Q03L	1	3	1	LPB7T8	f	f
19	Plastic	Plastic	3	\N	2025-09-02 08:56:39.038	2025-09-02 08:56:39.038	\N	aluminium	t	6UA7BE	1	1	1	\N	t	t
20	PE		3	\N	2025-09-02 08:56:39.047	2025-09-02 08:56:39.047	\N	aluminium	t	UA5IC2	1	2	1	6UA7BE	f	f
21	PE-A		3	\N	2025-09-02 08:56:39.05	2025-09-02 08:56:39.05	\N	aluminium	t	BGGVZT	1	3	1	UA5IC2	f	f
25	Plastic	Plastic	4	\N	2025-09-02 08:57:01.914	2025-09-02 08:57:01.914	\N	aluminium	t	DR9IPT	1	1	1	\N	t	t
26	PE		4	\N	2025-09-02 08:57:01.923	2025-09-02 08:57:01.923	\N	aluminium	t	GUDINT	1	2	1	DR9IPT	f	f
27	PE-A		4	\N	2025-09-02 08:57:01.925	2025-09-02 08:57:01.925	\N	aluminium	t	GM5W2R	1	3	1	GUDINT	f	f
\.


--
-- Data for Name: report_set_frequency; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_frequency (id, rhythm, created_at, updated_at, deleted_at, frequency, packaging_service_id) FROM stdin;
1	ANNUALLY	2025-09-02 08:57:59.048	2025-09-02 08:57:59.048	\N	{"open": {"day": 1, "month": "JANUARY"}, "deadline": {"day": 1, "month": "JANUARY"}}	1
2	QUARTERLY	2025-09-02 08:57:59.048	2025-09-02 08:57:59.048	\N	{"open": {"option": "FIRST", "weekDay": "MONDAY"}, "deadline": {"option": "LAST", "weekDay": "MONDAY"}}	1
3	QUARTERLY	2025-09-02 08:57:59.048	2025-09-02 08:57:59.048	\N	{"open": {"option": "FIRST", "weekDay": "MONDAY"}, "deadline": {"option": "LAST", "weekDay": "MONDAY"}}	2
\.


--
-- Data for Name: report_set_price_list; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_price_list (id, report_set_id, title, license_year, start_date, end_date, type, fixed_price, base_price, minimum_fee, created_at, updated_at, deleted_at) FROM stdin;
1	1	priceA	2025	2025-01-01 00:00:00	3001-01-01 00:00:00	PRICE_PER_VOLUME_BASE_PRICE	\N	1000	2000	2025-09-02 08:52:47.077	2025-09-02 08:52:47.077	\N
2	2	priceA	2025	2025-01-01 00:00:00	3001-01-01 00:00:00	PRICE_PER_VOLUME_BASE_PRICE	\N	1000	2000	2025-09-02 08:53:08.281	2025-09-02 08:53:46.057	\N
3	3	PriceB	2025	2025-01-01 00:00:00	3001-01-01 00:00:00	PRICE_PER_VOLUME_BASE_PRICE	\N	400	800	2025-09-02 08:56:39.073	2025-09-02 08:56:39.073	\N
4	4	PriceB	2025	2025-01-01 00:00:00	3001-01-01 00:00:00	PRICE_PER_VOLUME_BASE_PRICE	\N	400	800	2025-09-02 08:56:54.352	2025-09-02 08:57:01.957	\N
\.


--
-- Data for Name: report_set_price_list_item; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.report_set_price_list_item (id, price_list_id, fraction_code, price, created_at, updated_at, deleted_at) FROM stdin;
1	1	603T6C	30000	2025-09-02 08:52:47.11	2025-09-02 08:52:47.11	\N
2	1	47EP6R	40000	2025-09-02 08:52:47.112	2025-09-02 08:52:47.112	\N
3	1	MR822Y	50000	2025-09-02 08:52:47.113	2025-09-02 08:52:47.113	\N
4	1	8IIO4O	60000	2025-09-02 08:52:47.113	2025-09-02 08:52:47.113	\N
9	2	VUCNC5	30000	2025-09-02 08:53:46.048	2025-09-02 08:53:46.048	\N
10	2	IHB4FC	40000	2025-09-02 08:53:46.052	2025-09-02 08:53:46.052	\N
11	2	H5NZE1	50000	2025-09-02 08:53:46.054	2025-09-02 08:53:46.054	\N
12	2	V0Q03L	60000	2025-09-02 08:53:46.055	2025-09-02 08:53:46.055	\N
13	3	6UA7BE	9000	2025-09-02 08:56:39.082	2025-09-02 08:56:39.082	\N
14	3	BGGVZT	10000	2025-09-02 08:56:39.083	2025-09-02 08:56:39.083	\N
17	4	DR9IPT	9000	2025-09-02 08:57:01.948	2025-09-02 08:57:01.948	\N
18	4	GM5W2R	10000	2025-09-02 08:57:01.95	2025-09-02 08:57:01.95	\N
\.


--
-- Data for Name: representative_tier; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.representative_tier (id, name, price, country_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: required_information; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.required_information (id, country_id, type, name, description, created_at, updated_at, deleted_at, question, file_id, kind) FROM stdin;
1	1	TEXT	Require text for sales packaging	Require text for sales packaging help	2025-09-02 09:01:06.954	2025-09-02 09:01:06.954	\N	\N	\N	COUNTRY_INFORMATION
\.


--
-- Data for Name: required_information_packaging_service; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.required_information_packaging_service (required_information_id, packaging_service_id, created_at) FROM stdin;
1	1	2025-09-02 09:01:06.902365+00
\.


--
-- Data for Name: service_next_step; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.service_next_step (id, license_id, action_guide_id, title, available_date, deadline_date, done_at, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: settings; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.settings (id, key, value, term_or_condition_file_id, created_at, updated_at, deleted_at) FROM stdin;
1	INVITE_CUSTOMERS_MANAGER	{"validity": {"type": "month", "value": 12}, "linkPattern": "https://example.com/invite?code={code}", "carbonOffset": {"percentage": {"value": 5, "amount": 150, "serviceType": "year"}, "absoluteValue": null}, "couponPrefix": "WELCOME2025", "isLinkEnabled": true, "isVoucherEnabled": true, "maximumReferrals": 100, "minimumOrderValue": 50, "minimumOrderPeriod": {"type": "year", "value": 1}, "euLicenseDiscountType": "PERCENTAGE", "actionGuideDiscountType": "FREE_ITEM", "directLicenseDiscountType": "FIXED_AMOUNT"}	\N	2025-09-02 08:41:54.945	2025-09-02 08:41:54.945	\N
\.


--
-- Data for Name: shopping_cart; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.shopping_cart (id, cart_json, email, created_at, updated_at, deleted_at, invoice_id, is_churned, journey, journey_step, payment, status, total, subtotal, vat_percentage, vat_value, coupon_id, coupon_type, coupon_value, coupon_url_link, affiliate_link, affiliate_type, affiliate_customer_id, affiliate_partner_id, customer_id) FROM stdin;
8ed8e4fd-bbb1-4b07-b97e-452d2b74ef47	\N	<EMAIL>	2025-09-02 08:45:49.897	2025-09-02 08:45:49.897	\N	\N	f	LONG	SHOPPING_CART	\N	OPEN	0	0	0	0	\N	\N	0	\N	\N	\N	\N	\N	\N
\.


--
-- Data for Name: shopping_cart_item; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.shopping_cart_item (id, shopping_cart_id, service_type, specification_type, country_id, country_code, country_name, country_flag, year, price_list, packaging_services, created_at, updated_at, deleted_at, calculator, price) FROM stdin;
\.


--
-- Data for Name: termination; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.termination (id, created_at, updated_at, deleted_at, completed_at, requested_at, status) FROM stdin;
\.


--
-- Data for Name: termination_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.termination_reason (id, created_at, updated_at, deleted_at, termination, reason) FROM stdin;
\.


--
-- Data for Name: termination_termination_reason; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.termination_termination_reason (id, termination_id, reason_id, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: upload_file_history; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.upload_file_history (id, file_name, file_url, broker_id, created_at, deleted_at, updated_at) FROM stdin;
\.


--
-- Data for Name: user_access_request; Type: TABLE DATA; Schema: public; Owner: oneepr-local-user
--

COPY public.user_access_request (id, user_id, user_email, requester_id, status, token, callback_url, is_active, created_at, expires_at, updated_at, wrong_count) FROM stdin;
\.


--
-- Name: action_guide_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.action_guide_id_seq', 1, false);


--
-- Name: action_guide_price_list_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.action_guide_price_list_id_seq', 1, false);


--
-- Name: broker_company_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.broker_company_id_seq', 1, false);


--
-- Name: broker_company_order_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.broker_company_order_id_seq', 1, false);


--
-- Name: broker_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.broker_id_seq', 1, false);


--
-- Name: certificate_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.certificate_id_seq', 1, false);


--
-- Name: change_user_email_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.change_user_email_id_seq', 1, false);


--
-- Name: cluster_customers_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.cluster_customers_id_seq', 1, false);


--
-- Name: cluster_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.cluster_id_seq', 1, false);


--
-- Name: commission_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.commission_id_seq', 1, false);


--
-- Name: company_address_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.company_address_id_seq', 1, false);


--
-- Name: company_billing_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.company_billing_id_seq', 1, false);


--
-- Name: company_contact_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.company_contact_id_seq', 1, false);


--
-- Name: company_email_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.company_email_id_seq', 1, false);


--
-- Name: company_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.company_id_seq', 1, false);


--
-- Name: consent_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.consent_id_seq', 1, false);


--
-- Name: contract_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.contract_id_seq', 1, false);


--
-- Name: country_follower_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.country_follower_id_seq', 1, false);


--
-- Name: country_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.country_id_seq', 196, true);


--
-- Name: country_price_list_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.country_price_list_id_seq', 1, false);


--
-- Name: coupon_customer_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.coupon_customer_id_seq', 1, false);


--
-- Name: coupon_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.coupon_id_seq', 1, false);


--
-- Name: coupon_partners_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.coupon_partners_id_seq', 1, false);


--
-- Name: coupon_uses_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.coupon_uses_id_seq', 1, false);


--
-- Name: criteria_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.criteria_id_seq', 7, true);


--
-- Name: criteria_option_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.criteria_option_id_seq', 18, true);


--
-- Name: customer_activities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_activities_id_seq', 1, false);


--
-- Name: customer_commitment_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_commitment_id_seq', 1, false);


--
-- Name: customer_consent_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_consent_id_seq', 1, false);


--
-- Name: customer_document_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_document_id_seq', 1, false);


--
-- Name: customer_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_id_seq', 1, false);


--
-- Name: customer_invitation_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_invitation_id_seq', 1, false);


--
-- Name: customer_invite_token_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_invite_token_id_seq', 1, false);


--
-- Name: customer_phone_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_phone_id_seq', 1, false);


--
-- Name: customer_tutorial_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.customer_tutorial_id_seq', 1, false);


--
-- Name: decline_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.decline_id_seq', 1, false);


--
-- Name: decline_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.decline_reason_id_seq', 1, false);


--
-- Name: decline_to_decline_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.decline_to_decline_reason_id_seq', 1, false);


--
-- Name: fraction_icon_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.fraction_icon_id_seq', 1, true);


--
-- Name: general_information_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.general_information_id_seq', 1, false);


--
-- Name: license_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_id_seq', 1, false);


--
-- Name: license_other_cost_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_other_cost_id_seq', 1, false);


--
-- Name: license_packaging_service_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_packaging_service_id_seq', 1, false);


--
-- Name: license_price_list_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_price_list_id_seq', 1, false);


--
-- Name: license_report_set_frequency_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_report_set_frequency_id_seq', 1, false);


--
-- Name: license_report_set_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_report_set_id_seq', 1, false);


--
-- Name: license_representative_tier_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_representative_tier_id_seq', 1, false);


--
-- Name: license_required_information_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_required_information_id_seq', 1, false);


--
-- Name: license_third_party_invoice_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_third_party_invoice_id_seq', 1, false);


--
-- Name: license_volume_report_decline_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_volume_report_decline_reason_id_seq', 1, false);


--
-- Name: license_volume_report_error_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_volume_report_error_id_seq', 1, false);


--
-- Name: license_volume_report_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_volume_report_id_seq', 1, false);


--
-- Name: license_volume_report_item_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.license_volume_report_item_id_seq', 1, false);


--
-- Name: marketing_material_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.marketing_material_id_seq', 1, false);


--
-- Name: marketing_material_partner_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.marketing_material_partner_id_seq', 1, false);


--
-- Name: obligation_check_section_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.obligation_check_section_id_seq', 1, true);


--
-- Name: one_epr_role_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.one_epr_role_id_seq', 9, true);


--
-- Name: one_epr_user_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.one_epr_user_id_seq', 9, true);


--
-- Name: other_cost_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.other_cost_id_seq', 3, true);


--
-- Name: packaging_service_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.packaging_service_id_seq', 2, true);


--
-- Name: partner_banking_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.partner_banking_id_seq', 1, false);


--
-- Name: partner_contract_change_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.partner_contract_change_id_seq', 1, false);


--
-- Name: partner_contract_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.partner_contract_id_seq', 1, false);


--
-- Name: partner_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.partner_id_seq', 1, false);


--
-- Name: password_reset_request_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.password_reset_request_id_seq', 1, false);


--
-- Name: price_list_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.price_list_id_seq', 1, false);


--
-- Name: reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.reason_id_seq', 1, false);


--
-- Name: recommended_country_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.recommended_country_id_seq', 1, false);


--
-- Name: refresh_token_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.refresh_token_id_seq', 1, false);


--
-- Name: report_decline_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_decline_reason_id_seq', 1, false);


--
-- Name: report_set_column_fraction_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_column_fraction_id_seq', 6, true);


--
-- Name: report_set_column_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_column_id_seq', 12, true);


--
-- Name: report_set_fraction_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_fraction_id_seq', 27, true);


--
-- Name: report_set_frequency_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_frequency_id_seq', 3, true);


--
-- Name: report_set_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_id_seq', 4, true);


--
-- Name: report_set_price_list_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_price_list_id_seq', 4, true);


--
-- Name: report_set_price_list_item_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.report_set_price_list_item_id_seq', 18, true);


--
-- Name: representative_tier_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.representative_tier_id_seq', 1, false);


--
-- Name: required_information_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.required_information_id_seq', 1, true);


--
-- Name: service_next_step_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.service_next_step_id_seq', 1, false);


--
-- Name: settings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.settings_id_seq', 1, true);


--
-- Name: shopping_cart_item_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.shopping_cart_item_id_seq', 1, false);


--
-- Name: termination_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.termination_id_seq', 1, false);


--
-- Name: termination_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.termination_reason_id_seq', 1, false);


--
-- Name: termination_termination_reason_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.termination_termination_reason_id_seq', 1, false);


--
-- Name: upload_file_history_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.upload_file_history_id_seq', 1, false);


--
-- Name: user_access_request_id_seq; Type: SEQUENCE SET; Schema: public; Owner: oneepr-local-user
--

SELECT pg_catalog.setval('public.user_access_request_id_seq', 1, false);


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: action_guide action_guide_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_pkey PRIMARY KEY (id);


--
-- Name: action_guide_price_list action_guide_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list
    ADD CONSTRAINT action_guide_price_list_pkey PRIMARY KEY (id);


--
-- Name: broker_company_order broker_company_order_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_pkey PRIMARY KEY (id);


--
-- Name: broker_company broker_company_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_pkey PRIMARY KEY (id);


--
-- Name: broker broker_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker
    ADD CONSTRAINT broker_pkey PRIMARY KEY (id);


--
-- Name: certificate certificate_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.certificate
    ADD CONSTRAINT certificate_pkey PRIMARY KEY (id);


--
-- Name: cluster_customers cluster_customers_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_pkey PRIMARY KEY (id);


--
-- Name: cluster cluster_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster
    ADD CONSTRAINT cluster_pkey PRIMARY KEY (id);


--
-- Name: commission commission_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_pkey PRIMARY KEY (id);


--
-- Name: company_address company_address_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_address
    ADD CONSTRAINT company_address_pkey PRIMARY KEY (id);


--
-- Name: company_contact company_contact_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_contact
    ADD CONSTRAINT company_contact_pkey PRIMARY KEY (id);


--
-- Name: company_email company_email_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_email
    ADD CONSTRAINT company_email_pkey PRIMARY KEY (id);


--
-- Name: company company_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_pkey PRIMARY KEY (id);


--
-- Name: consent consent_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.consent
    ADD CONSTRAINT consent_pkey PRIMARY KEY (id);


--
-- Name: contract contract_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_pkey PRIMARY KEY (id);


--
-- Name: country_follower country_follower_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_follower
    ADD CONSTRAINT country_follower_pkey PRIMARY KEY (id);


--
-- Name: country country_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country
    ADD CONSTRAINT country_pkey PRIMARY KEY (id);


--
-- Name: country_price_list country_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_pkey PRIMARY KEY (id);


--
-- Name: coupon_customer coupon_customer_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_pkey PRIMARY KEY (id);


--
-- Name: coupon_partners coupon_partners_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_pkey PRIMARY KEY (id);


--
-- Name: coupon coupon_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon
    ADD CONSTRAINT coupon_pkey PRIMARY KEY (id);


--
-- Name: coupon_uses coupon_uses_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_pkey PRIMARY KEY (id);


--
-- Name: criteria_option criteria_option_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ADD CONSTRAINT criteria_option_pkey PRIMARY KEY (id);


--
-- Name: criteria criteria_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_pkey PRIMARY KEY (id);


--
-- Name: customer_activities customer_activities_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_activities
    ADD CONSTRAINT customer_activities_pkey PRIMARY KEY (id);


--
-- Name: customer_commitment customer_commitment_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_commitment
    ADD CONSTRAINT customer_commitment_pkey PRIMARY KEY (id);


--
-- Name: customer_consent customer_consent_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_pkey PRIMARY KEY (id);


--
-- Name: customer_document customer_document_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_document
    ADD CONSTRAINT customer_document_pkey PRIMARY KEY (id);


--
-- Name: customer_invitation customer_invitation_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_pkey PRIMARY KEY (id);


--
-- Name: customer_invite_token customer_invite_token_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invite_token
    ADD CONSTRAINT customer_invite_token_pkey PRIMARY KEY (id);


--
-- Name: customer_phone customer_phone_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_phone
    ADD CONSTRAINT customer_phone_pkey PRIMARY KEY (id);


--
-- Name: customer customer_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer
    ADD CONSTRAINT customer_pkey PRIMARY KEY (id);


--
-- Name: customer_tutorial customer_tutorial_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_tutorial
    ADD CONSTRAINT customer_tutorial_pkey PRIMARY KEY (id);


--
-- Name: decline_to_decline_reason decline_to_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: file file_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_pkey PRIMARY KEY (id);


--
-- Name: files files_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.files
    ADD CONSTRAINT files_pkey PRIMARY KEY (id);


--
-- Name: flyway_schema_history flyway_schema_history_pk; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.flyway_schema_history
    ADD CONSTRAINT flyway_schema_history_pk PRIMARY KEY (installed_rank);


--
-- Name: fraction_icon fraction_icon_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.fraction_icon
    ADD CONSTRAINT fraction_icon_pkey PRIMARY KEY (id);


--
-- Name: general_information general_information_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.general_information
    ADD CONSTRAINT general_information_pkey PRIMARY KEY (id);


--
-- Name: license_other_cost license_other_cost_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_other_cost
    ADD CONSTRAINT license_other_cost_pkey PRIMARY KEY (id);


--
-- Name: license_packaging_service license_packaging_service_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_packaging_service
    ADD CONSTRAINT license_packaging_service_pkey PRIMARY KEY (id);


--
-- Name: license license_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_pkey PRIMARY KEY (id);


--
-- Name: license_price_list license_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_price_list
    ADD CONSTRAINT license_price_list_pkey PRIMARY KEY (id);


--
-- Name: license_report_set_frequency license_report_set_frequency_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency
    ADD CONSTRAINT license_report_set_frequency_pkey PRIMARY KEY (id);


--
-- Name: license_report_set license_report_set_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set
    ADD CONSTRAINT license_report_set_pkey PRIMARY KEY (id);


--
-- Name: license_representative_tier license_representative_tier_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_representative_tier
    ADD CONSTRAINT license_representative_tier_pkey PRIMARY KEY (id);


--
-- Name: license_required_information license_required_information_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_pkey PRIMARY KEY (id);


--
-- Name: license_third_party_invoice license_third_party_invoice_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice
    ADD CONSTRAINT license_third_party_invoice_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_error license_volume_report_error_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report_item license_volume_report_item_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item
    ADD CONSTRAINT license_volume_report_item_pkey PRIMARY KEY (id);


--
-- Name: license_volume_report license_volume_report_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report
    ADD CONSTRAINT license_volume_report_pkey PRIMARY KEY (id);


--
-- Name: marketing_material_partner marketing_material_partner_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_pkey PRIMARY KEY (id);


--
-- Name: marketing_material marketing_material_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material
    ADD CONSTRAINT marketing_material_pkey PRIMARY KEY (id);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: obligation_check_section obligation_check_section_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.obligation_check_section
    ADD CONSTRAINT obligation_check_section_pkey PRIMARY KEY (id);


--
-- Name: other_cost other_cost_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.other_cost
    ADD CONSTRAINT other_cost_pkey PRIMARY KEY (id);


--
-- Name: packaging_service packaging_service_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service
    ADD CONSTRAINT packaging_service_pkey PRIMARY KEY (id);


--
-- Name: partner_banking partner_banking_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_banking
    ADD CONSTRAINT partner_banking_pkey PRIMARY KEY (id);


--
-- Name: partner_contract_change partner_contract_change_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract_change
    ADD CONSTRAINT partner_contract_change_pkey PRIMARY KEY (id);


--
-- Name: partner_contract partner_contract_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract
    ADD CONSTRAINT partner_contract_pkey PRIMARY KEY (id);


--
-- Name: partner partner_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner
    ADD CONSTRAINT partner_pkey PRIMARY KEY (id);


--
-- Name: decline pk_decline; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT pk_decline PRIMARY KEY (id);


--
-- Name: decline_reason pk_decline_reason; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_reason
    ADD CONSTRAINT pk_decline_reason PRIMARY KEY (id);


--
-- Name: packaging_service_criteria_option pk_packaging_service_criteria_option; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service_criteria_option
    ADD CONSTRAINT pk_packaging_service_criteria_option PRIMARY KEY (packaging_service_id, criteria_option_id);


--
-- Name: reason pk_reason; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.reason
    ADD CONSTRAINT pk_reason PRIMARY KEY (id);


--
-- Name: termination pk_termination; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination
    ADD CONSTRAINT pk_termination PRIMARY KEY (id);


--
-- Name: termination_reason pk_termination_reason; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_reason
    ADD CONSTRAINT pk_termination_reason PRIMARY KEY (id);


--
-- Name: price_list price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.price_list
    ADD CONSTRAINT price_list_pkey PRIMARY KEY (id);


--
-- Name: recommended_country recommended_country_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.recommended_country
    ADD CONSTRAINT recommended_country_pkey PRIMARY KEY (id);


--
-- Name: refresh_token refresh_token_token_key; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.refresh_token
    ADD CONSTRAINT refresh_token_token_key UNIQUE (token);


--
-- Name: refresh_token refresh_token_user_id_key; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.refresh_token
    ADD CONSTRAINT refresh_token_user_id_key UNIQUE (user_id);


--
-- Name: report_decline_reason report_decline_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_decline_reason
    ADD CONSTRAINT report_decline_reason_pkey PRIMARY KEY (id);


--
-- Name: report_set_column_fraction report_set_column_fraction_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_pkey PRIMARY KEY (id);


--
-- Name: report_set_column report_set_column_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_pkey PRIMARY KEY (id);


--
-- Name: report_set_fraction report_set_fraction_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_pkey PRIMARY KEY (id);


--
-- Name: report_set_frequency report_set_frequency_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_frequency
    ADD CONSTRAINT report_set_frequency_pkey PRIMARY KEY (id);


--
-- Name: report_set report_set_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_pkey PRIMARY KEY (id);


--
-- Name: report_set_price_list_item report_set_price_list_item_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_pkey PRIMARY KEY (id);


--
-- Name: report_set_price_list report_set_price_list_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list
    ADD CONSTRAINT report_set_price_list_pkey PRIMARY KEY (id);


--
-- Name: representative_tier representative_tier_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.representative_tier
    ADD CONSTRAINT representative_tier_pkey PRIMARY KEY (id);


--
-- Name: required_information_packaging_service required_information_packaging_service_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information_packaging_service
    ADD CONSTRAINT required_information_packaging_service_pkey PRIMARY KEY (required_information_id, packaging_service_id);


--
-- Name: required_information required_information_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_pkey PRIMARY KEY (id);


--
-- Name: service_next_step service_next_step_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_pkey PRIMARY KEY (id);


--
-- Name: settings settings_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_pkey PRIMARY KEY (id);


--
-- Name: shopping_cart_item shopping_cart_item_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item
    ADD CONSTRAINT shopping_cart_item_pkey PRIMARY KEY (id);


--
-- Name: shopping_cart shopping_cart_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_pkey PRIMARY KEY (id);


--
-- Name: termination_termination_reason termination_termination_reason_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_termination_reason
    ADD CONSTRAINT termination_termination_reason_pkey PRIMARY KEY (id);


--
-- Name: upload_file_history upload_file_history_pkey; Type: CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.upload_file_history
    ADD CONSTRAINT upload_file_history_pkey PRIMARY KEY (id);


--
-- Name: broker_email_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX broker_email_key ON public.broker USING btree (email);


--
-- Name: broker_user_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX broker_user_id_key ON public.broker USING btree (user_id);


--
-- Name: company_billing_company_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX company_billing_company_id_key ON public.company_billing USING btree (company_id);


--
-- Name: company_contact_company_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX company_contact_company_id_key ON public.company_contact USING btree (company_id);


--
-- Name: country_code_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX country_code_key ON public.country USING btree (code);


--
-- Name: country_name_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX country_name_key ON public.country USING btree (name);


--
-- Name: coupon_code_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX coupon_code_key ON public.coupon USING btree (code);


--
-- Name: coupon_link_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX coupon_link_key ON public.coupon USING btree (link);


--
-- Name: customer_commitment_customer_email_country_code_year_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_commitment_customer_email_country_code_year_key ON public.customer_commitment USING btree (customer_email, country_code, year);


--
-- Name: customer_consent_customer_id_consent_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_consent_customer_id_consent_id_key ON public.customer_consent USING btree (customer_id, consent_id);


--
-- Name: customer_email_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_email_key ON public.customer USING btree (email);


--
-- Name: customer_invite_token_customer_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_invite_token_customer_id_key ON public.customer_invite_token USING btree (customer_id);


--
-- Name: customer_tutorial_customer_id_service_type_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_tutorial_customer_id_service_type_key ON public.customer_tutorial USING btree (customer_id, service_type);


--
-- Name: customer_unique; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX customer_unique ON public.customer USING btree (user_id);


--
-- Name: decline_license_required_information_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX decline_license_required_information_id_key ON public.decline USING btree (license_required_information_id);


--
-- Name: decline_license_volume_report_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX decline_license_volume_report_id_key ON public.decline USING btree (license_volume_report_id);


--
-- Name: decline_license_volume_report_item_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX decline_license_volume_report_item_id_key ON public.decline USING btree (license_volume_report_item_id);


--
-- Name: flyway_schema_history_s_idx; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX flyway_schema_history_s_idx ON public.flyway_schema_history USING btree (success);


--
-- Name: fraction_icon_file_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX fraction_icon_file_id_key ON public.fraction_icon USING btree (file_id);


--
-- Name: idx_criteria_obligation_check_section_id; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_criteria_obligation_check_section_id ON public.criteria USING btree (obligation_check_section_id);


--
-- Name: idx_criteria_option_optional_criteria_id; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_criteria_option_optional_criteria_id ON public.criteria_option USING btree (optional_criteria_id);


--
-- Name: idx_customer_activities_created_at; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_customer_activities_created_at ON public.customer_activities USING btree (created_at);


--
-- Name: idx_customer_activities_customer_id; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_customer_activities_customer_id ON public.customer_activities USING btree (customer_id);


--
-- Name: idx_customer_activities_metadata_gin; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_customer_activities_metadata_gin ON public.customer_activities USING gin (metadata);


--
-- Name: idx_customer_activities_type; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_customer_activities_type ON public.customer_activities USING btree (type);


--
-- Name: idx_obligation_check_section_country_id; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_obligation_check_section_country_id ON public.obligation_check_section USING btree (country_id);


--
-- Name: idx_required_information_packaging_service_packaging_service_id; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_required_information_packaging_service_packaging_service_id ON public.required_information_packaging_service USING btree (packaging_service_id);


--
-- Name: idx_required_information_packaging_service_required_information; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE INDEX idx_required_information_packaging_service_required_information ON public.required_information_packaging_service USING btree (required_information_id);


--
-- Name: license_registration_and_termination_monday_ref_unique; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX license_registration_and_termination_monday_ref_unique ON public.license USING btree (registration_and_termination_monday_ref);


--
-- Name: license_report_set_frequency_license_packaging_service_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX license_report_set_frequency_license_packaging_service_id_key ON public.license_report_set_frequency USING btree (license_packaging_service_id);


--
-- Name: license_report_set_license_packaging_service_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX license_report_set_license_packaging_service_id_key ON public.license_report_set USING btree (license_packaging_service_id);


--
-- Name: license_third_party_invoice_unique; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX license_third_party_invoice_unique ON public.license_third_party_invoice USING btree (third_party_invoice_monday_ref);


--
-- Name: partner_banking_partner_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX partner_banking_partner_id_key ON public.partner_banking USING btree (partner_id);


--
-- Name: partner_contract_partner_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX partner_contract_partner_id_key ON public.partner_contract USING btree (partner_id);


--
-- Name: report_set_column_code_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX report_set_column_code_key ON public.report_set_column USING btree (code);


--
-- Name: report_set_fraction_code_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX report_set_fraction_code_key ON public.report_set_fraction USING btree (code);


--
-- Name: report_set_sheet_file_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX report_set_sheet_file_id_key ON public.report_set USING btree (sheet_file_id);


--
-- Name: required_information_file_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX required_information_file_id_key ON public.required_information USING btree (file_id);


--
-- Name: settings_key_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX settings_key_key ON public.settings USING btree (key);


--
-- Name: settings_term_or_condition_file_id_key; Type: INDEX; Schema: public; Owner: oneepr-local-user
--

CREATE UNIQUE INDEX settings_term_or_condition_file_id_key ON public.settings USING btree (term_or_condition_file_id);


--
-- Name: action_guide action_guide_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: action_guide_price_list action_guide_price_list_action_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide_price_list
    ADD CONSTRAINT action_guide_price_list_action_guide_id_fkey FOREIGN KEY (action_guide_id) REFERENCES public.action_guide(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: action_guide action_guide_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.action_guide
    ADD CONSTRAINT action_guide_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: broker_company broker_company_broker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.broker(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: broker_company broker_company_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company
    ADD CONSTRAINT broker_company_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.upload_file_history(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: broker_company_order broker_company_order_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.broker_company(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: broker_company_order broker_company_order_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.broker_company_order
    ADD CONSTRAINT broker_company_order_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.upload_file_history(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: certificate certificate_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.certificate
    ADD CONSTRAINT certificate_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: cluster_customers cluster_customers_cluster_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_cluster_id_fkey FOREIGN KEY (cluster_id) REFERENCES public.cluster(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: cluster_customers cluster_customers_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.cluster_customers
    ADD CONSTRAINT cluster_customers_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: commission commission_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: commission commission_order_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.commission
    ADD CONSTRAINT commission_order_customer_id_fkey FOREIGN KEY (order_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_address_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_address_id_fkey FOREIGN KEY (address_id) REFERENCES public.company_address(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company_contact company_contact_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_contact
    ADD CONSTRAINT company_contact_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company_email company_email_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company_email
    ADD CONSTRAINT company_email_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.company(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: company company_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.company
    ADD CONSTRAINT company_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: contract contract_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: contract contract_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.contract
    ADD CONSTRAINT contract_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: country_follower country_follower_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_follower
    ADD CONSTRAINT country_follower_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: country_price_list country_price_list_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: country_price_list country_price_list_price_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.country_price_list
    ADD CONSTRAINT country_price_list_price_list_id_fkey FOREIGN KEY (price_list_id) REFERENCES public.price_list(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_customer coupon_customer_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_customer coupon_customer_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_customer
    ADD CONSTRAINT coupon_customer_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_partners coupon_partners_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_partners coupon_partners_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_partners
    ADD CONSTRAINT coupon_partners_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_uses coupon_uses_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: coupon_uses coupon_uses_shopping_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.coupon_uses
    ADD CONSTRAINT coupon_uses_shopping_cart_id_fkey FOREIGN KEY (shopping_cart_id) REFERENCES public.shopping_cart(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria criteria_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria_option criteria_option_criteria_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ADD CONSTRAINT criteria_option_criteria_id_fkey FOREIGN KEY (criteria_id) REFERENCES public.criteria(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: criteria criteria_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: criteria criteria_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT criteria_required_information_id_fkey FOREIGN KEY (required_information_id) REFERENCES public.required_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_commitment customer_commitment_customer_email_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_commitment
    ADD CONSTRAINT customer_commitment_customer_email_fkey FOREIGN KEY (customer_email) REFERENCES public.customer(email) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_consent customer_consent_consent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_consent_id_fkey FOREIGN KEY (consent_id) REFERENCES public.consent(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_consent customer_consent_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_consent
    ADD CONSTRAINT customer_consent_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_document customer_document_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_document
    ADD CONSTRAINT customer_document_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_invitation customer_invitation_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: customer_invitation customer_invitation_invited_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invitation
    ADD CONSTRAINT customer_invitation_invited_customer_id_fkey FOREIGN KEY (invited_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_invite_token customer_invite_token_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_invite_token
    ADD CONSTRAINT customer_invite_token_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_phone customer_phone_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_phone
    ADD CONSTRAINT customer_phone_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: customer_tutorial customer_tutorial_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_tutorial
    ADD CONSTRAINT customer_tutorial_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: decline decline_license_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_required_information_id_fkey FOREIGN KEY (license_required_information_id) REFERENCES public.license_required_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline decline_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline decline_license_volume_report_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT decline_license_volume_report_item_id_fkey FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: decline_reason decline_reason_decline_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_reason
    ADD CONSTRAINT decline_reason_decline_id_fkey FOREIGN KEY (decline_id) REFERENCES public.decline(id);


--
-- Name: decline_to_decline_reason decline_to_decline_reason_decline_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_decline_id_fkey FOREIGN KEY (decline_id) REFERENCES public.decline(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: decline_to_decline_reason decline_to_decline_reason_reason_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT decline_to_decline_reason_reason_id_fkey FOREIGN KEY (reason_id) REFERENCES public.reason(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: file file_certificate_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_certificate_id_fkey FOREIGN KEY (certificate_id) REFERENCES public.certificate(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_general_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_general_information_id_fkey FOREIGN KEY (general_information_id) REFERENCES public.general_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_marketing_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_marketing_material_id_fkey FOREIGN KEY (marketing_material_id) REFERENCES public.marketing_material(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_partner_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_partner_contract_id_fkey FOREIGN KEY (partner_contract_id) REFERENCES public.partner_contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_required_information_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_required_information_id_fkey FOREIGN KEY (required_information_id) REFERENCES public.license_required_information(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: file file_third_party_invoice_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.file
    ADD CONSTRAINT file_third_party_invoice_id_fkey FOREIGN KEY (third_party_invoice_id) REFERENCES public.license_third_party_invoice(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: criteria fk_criteria_obligation_check_section; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria
    ADD CONSTRAINT fk_criteria_obligation_check_section FOREIGN KEY (obligation_check_section_id) REFERENCES public.obligation_check_section(id) ON DELETE SET NULL;


--
-- Name: criteria_option fk_criteria_option_on_optional_criteria; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.criteria_option
    ADD CONSTRAINT fk_criteria_option_on_optional_criteria FOREIGN KEY (optional_criteria_id) REFERENCES public.criteria(id) ON DELETE RESTRICT;


--
-- Name: customer_activities fk_customer_activities_customer; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_activities
    ADD CONSTRAINT fk_customer_activities_customer FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON DELETE RESTRICT;


--
-- Name: customer_commitment fk_customer_commitment_on_shopping_cart; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.customer_commitment
    ADD CONSTRAINT fk_customer_commitment_on_shopping_cart FOREIGN KEY (shopping_cart_id) REFERENCES public.shopping_cart(id);


--
-- Name: decline_reason fk_decline; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_reason
    ADD CONSTRAINT fk_decline FOREIGN KEY (decline_id) REFERENCES public.decline(id);


--
-- Name: decline fk_decline_to_required_information; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT fk_decline_to_required_information FOREIGN KEY (license_required_information_id) REFERENCES public.license_required_information(id) ON DELETE SET NULL;


--
-- Name: decline fk_decline_to_volume_report; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT fk_decline_to_volume_report FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON DELETE SET NULL;


--
-- Name: decline fk_decline_to_volume_report_item; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline
    ADD CONSTRAINT fk_decline_to_volume_report_item FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item(id) ON DELETE SET NULL;


--
-- Name: decline_to_decline_reason fk_join_to_decline; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT fk_join_to_decline FOREIGN KEY (decline_id) REFERENCES public.decline(id) ON DELETE CASCADE;


--
-- Name: decline_to_decline_reason fk_join_to_reason; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_to_decline_reason
    ADD CONSTRAINT fk_join_to_reason FOREIGN KEY (reason_id) REFERENCES public.reason(id) ON DELETE RESTRICT;


--
-- Name: obligation_check_section fk_obligation_check_section_country; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.obligation_check_section
    ADD CONSTRAINT fk_obligation_check_section_country FOREIGN KEY (country_id) REFERENCES public.country(id) ON DELETE RESTRICT;


--
-- Name: packaging_service_criteria_option fk_psco_on_criteria_option; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service_criteria_option
    ADD CONSTRAINT fk_psco_on_criteria_option FOREIGN KEY (criteria_option_id) REFERENCES public.criteria_option(id) ON DELETE CASCADE;


--
-- Name: packaging_service_criteria_option fk_psco_on_packaging_service; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service_criteria_option
    ADD CONSTRAINT fk_psco_on_packaging_service FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service(id) ON DELETE CASCADE;


--
-- Name: decline_reason fk_reason; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.decline_reason
    ADD CONSTRAINT fk_reason FOREIGN KEY (reason_id) REFERENCES public.reason(id);


--
-- Name: required_information_packaging_service fk_required_information_packaging_service_packaging_service; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information_packaging_service
    ADD CONSTRAINT fk_required_information_packaging_service_packaging_service FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service(id) ON DELETE CASCADE;


--
-- Name: required_information_packaging_service fk_required_information_packaging_service_required_information; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information_packaging_service
    ADD CONSTRAINT fk_required_information_packaging_service_required_information FOREIGN KEY (required_information_id) REFERENCES public.required_information(id) ON DELETE CASCADE;


--
-- Name: termination_reason fk_termination_reason_reason; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_reason
    ADD CONSTRAINT fk_termination_reason_reason FOREIGN KEY (reason) REFERENCES public.reason(id);


--
-- Name: termination_reason fk_termination_reason_termination; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_reason
    ADD CONSTRAINT fk_termination_reason_termination FOREIGN KEY (termination) REFERENCES public.termination(id);


--
-- Name: termination_termination_reason fk_termination_reason_to_reason; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_termination_reason
    ADD CONSTRAINT fk_termination_reason_to_reason FOREIGN KEY (reason_id) REFERENCES public.reason(id) ON DELETE RESTRICT;


--
-- Name: termination_termination_reason fk_termination_reason_to_termination; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.termination_termination_reason
    ADD CONSTRAINT fk_termination_reason_to_termination FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON DELETE CASCADE;


--
-- Name: fraction_icon fraction_icon_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.fraction_icon
    ADD CONSTRAINT fraction_icon_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license license_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_other_cost license_other_cost_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_other_cost
    ADD CONSTRAINT license_other_cost_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_packaging_service license_packaging_service_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_packaging_service
    ADD CONSTRAINT license_packaging_service_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_price_list license_price_list_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_price_list
    ADD CONSTRAINT license_price_list_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_report_set_frequency license_report_set_frequency_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set_frequency
    ADD CONSTRAINT license_report_set_frequency_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_report_set license_report_set_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_report_set
    ADD CONSTRAINT license_report_set_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_representative_tier license_representative_tier_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_representative_tier
    ADD CONSTRAINT license_representative_tier_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_required_information license_required_information_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_contract_id_fkey FOREIGN KEY (contract_id) REFERENCES public.contract(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_required_information license_required_information_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_required_information
    ADD CONSTRAINT license_required_information_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license license_termination_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license
    ADD CONSTRAINT license_termination_id_fkey FOREIGN KEY (termination_id) REFERENCES public.termination(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_third_party_invoice license_third_party_invoice_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_third_party_invoice
    ADD CONSTRAINT license_third_party_invoice_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_license_volume_report_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_license_volume_report_fkey FOREIGN KEY (license_volume_report_error_id) REFERENCES public.license_volume_report_error(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report_decline_reason license_volume_report_decline_reason_report_decline_reason_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_decline_reason
    ADD CONSTRAINT license_volume_report_decline_reason_report_decline_reason_fkey FOREIGN KEY (report_decline_reason_id) REFERENCES public.report_decline_reason(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report_error license_volume_report_error_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_error license_volume_report_error_license_volume_report_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_error
    ADD CONSTRAINT license_volume_report_error_license_volume_report_item_id_fkey FOREIGN KEY (license_volume_report_item_id) REFERENCES public.license_volume_report_item(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: license_volume_report_item license_volume_report_item_license_volume_report_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report_item
    ADD CONSTRAINT license_volume_report_item_license_volume_report_id_fkey FOREIGN KEY (license_volume_report_id) REFERENCES public.license_volume_report(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: license_volume_report license_volume_report_license_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.license_volume_report
    ADD CONSTRAINT license_volume_report_license_packaging_service_id_fkey FOREIGN KEY (license_packaging_service_id) REFERENCES public.license_packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: marketing_material_partner marketing_material_partner_marketing_material_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_marketing_material_id_fkey FOREIGN KEY (marketing_material_id) REFERENCES public.marketing_material(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: marketing_material_partner marketing_material_partner_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.marketing_material_partner
    ADD CONSTRAINT marketing_material_partner_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: other_cost other_cost_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.other_cost
    ADD CONSTRAINT other_cost_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: packaging_service packaging_service_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.packaging_service
    ADD CONSTRAINT packaging_service_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: partner_banking partner_banking_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_banking
    ADD CONSTRAINT partner_banking_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: partner_contract_change partner_contract_change_partner_contract_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract_change
    ADD CONSTRAINT partner_contract_change_partner_contract_id_fkey FOREIGN KEY (partner_contract_id) REFERENCES public.partner_contract(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: partner_contract partner_contract_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.partner_contract
    ADD CONSTRAINT partner_contract_partner_id_fkey FOREIGN KEY (partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: recommended_country recommended_country_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.recommended_country
    ADD CONSTRAINT recommended_country_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_column_fraction report_set_column_fraction_column_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_column_code_fkey FOREIGN KEY (column_code) REFERENCES public.report_set_column(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_column_fraction report_set_column_fraction_fraction_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column_fraction
    ADD CONSTRAINT report_set_column_fraction_fraction_code_fkey FOREIGN KEY (fraction_code) REFERENCES public.report_set_fraction(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_column report_set_column_parent_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_parent_code_fkey FOREIGN KEY (parent_code) REFERENCES public.report_set_column(code) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_column report_set_column_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_column
    ADD CONSTRAINT report_set_column_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_fraction report_set_fraction_fraction_icon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_fraction_icon_id_fkey FOREIGN KEY (fraction_icon_id) REFERENCES public.fraction_icon(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_fraction report_set_fraction_parent_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_parent_code_fkey FOREIGN KEY (parent_code) REFERENCES public.report_set_fraction(code) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: report_set_fraction report_set_fraction_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_fraction
    ADD CONSTRAINT report_set_fraction_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_frequency report_set_frequency_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_frequency
    ADD CONSTRAINT report_set_frequency_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set report_set_packaging_service_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_packaging_service_id_fkey FOREIGN KEY (packaging_service_id) REFERENCES public.packaging_service(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list_item report_set_price_list_item_fraction_code_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_fraction_code_fkey FOREIGN KEY (fraction_code) REFERENCES public.report_set_fraction(code) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list_item report_set_price_list_item_price_list_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list_item
    ADD CONSTRAINT report_set_price_list_item_price_list_id_fkey FOREIGN KEY (price_list_id) REFERENCES public.report_set_price_list(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set_price_list report_set_price_list_report_set_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set_price_list
    ADD CONSTRAINT report_set_price_list_report_set_id_fkey FOREIGN KEY (report_set_id) REFERENCES public.report_set(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: report_set report_set_sheet_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.report_set
    ADD CONSTRAINT report_set_sheet_file_id_fkey FOREIGN KEY (sheet_file_id) REFERENCES public.files(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: representative_tier representative_tier_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.representative_tier
    ADD CONSTRAINT representative_tier_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: required_information required_information_country_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_country_id_fkey FOREIGN KEY (country_id) REFERENCES public.country(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: required_information required_information_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.required_information
    ADD CONSTRAINT required_information_file_id_fkey FOREIGN KEY (file_id) REFERENCES public.files(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: service_next_step service_next_step_action_guide_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_action_guide_id_fkey FOREIGN KEY (action_guide_id) REFERENCES public.action_guide(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: service_next_step service_next_step_license_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.service_next_step
    ADD CONSTRAINT service_next_step_license_id_fkey FOREIGN KEY (license_id) REFERENCES public.license(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: settings settings_term_or_condition_file_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.settings
    ADD CONSTRAINT settings_term_or_condition_file_id_fkey FOREIGN KEY (term_or_condition_file_id) REFERENCES public.files(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_affiliate_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_affiliate_customer_id_fkey FOREIGN KEY (affiliate_customer_id) REFERENCES public.customer(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_affiliate_partner_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_affiliate_partner_id_fkey FOREIGN KEY (affiliate_partner_id) REFERENCES public.partner(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart shopping_cart_coupon_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart
    ADD CONSTRAINT shopping_cart_coupon_id_fkey FOREIGN KEY (coupon_id) REFERENCES public.coupon(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: shopping_cart_item shopping_cart_item_shopping_cart_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.shopping_cart_item
    ADD CONSTRAINT shopping_cart_item_shopping_cart_id_fkey FOREIGN KEY (shopping_cart_id) REFERENCES public.shopping_cart(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: upload_file_history upload_file_history_broker_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: oneepr-local-user
--

ALTER TABLE ONLY public.upload_file_history
    ADD CONSTRAINT upload_file_history_broker_id_fkey FOREIGN KEY (broker_id) REFERENCES public.broker(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- PostgreSQL database dump complete
--

