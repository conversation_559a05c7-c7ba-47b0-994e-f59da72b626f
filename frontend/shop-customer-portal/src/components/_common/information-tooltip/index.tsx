import { ReactNode } from "react";
import { Help } from "@arthursenno/lizenzero-ui-react/Icon";
import { cn } from "@/lib/utils";

interface InformationTooltipProps {
  children: ReactNode;
  className?: string;
  icon?: React.ReactElement;
}

export function InformationTooltip({ children, className, icon }: InformationTooltipProps) {
  return (
    <div className="relative group">
      {icon ? icon : <Help className={cn("size-5 fill-[#808FA9] hover:fill-support-blue border-white", className)} />}
      <div className="absolute top-6 left-0 invisible group-hover:visible bg-white shadow-elevation-04-1 p-5 rounded-[16px] w-[280px] z-50">
        {children}
      </div>
    </div>
  );
}

interface InformationTooltipDescriptionProps {
  children: ReactNode;
  className?: string;
}

export function InformationTooltipDescription({ children, className }: InformationTooltipDescriptionProps) {
  return <div className={cn("text-primary px-5 py-5", className)}>{children}</div>;
}
