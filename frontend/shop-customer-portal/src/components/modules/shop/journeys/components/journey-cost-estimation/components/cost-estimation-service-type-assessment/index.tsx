"use client";

import { memo, useCallback } from "react";
import { CostEstimationServiceTypeAssessmentCommitment } from "./cost-estimation-service-type-assessment-commitment";
import { CostEstimationServiceTypeAssessmentCalculator } from "./cost-estimation-service-type-assessment-calculator";
import {
  CostEstimationServiceTypeAssessmentPackagingService,
  CostEstimationServiceTypeAssessmentPackagingServiceFraction,
  CostEstimationCommitment,
  useSearchParamsSteps,
} from "../../common";

interface CostEstimationServiceTypeAssessmentProps {
  commitments: CostEstimationCommitment[];
  onSubmitCommitments: (commitments: CostEstimationCommitment[]) => Promise<void>;
  onFractionsChange: (
    id: number,
    fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[]
  ) => Promise<void>;
  packagingService: CostEstimationServiceTypeAssessmentPackagingService;
}
export const CostEstimationServiceTypeAssessment = memo(function CostEstimationServiceTypeAssessment(
  props: CostEstimationServiceTypeAssessmentProps
) {
  const { commitments, onSubmitCommitments, packagingService, onFractionsChange } = props;

  const {
    steps: [serviceStep],
    setStep,
  } = useSearchParamsSteps(["serviceStep"], ([serviceStep]) => {
    const isValidServiceStep = serviceStep && ["commitment", "calculator"].includes(serviceStep);
    if (!isValidServiceStep) {
      setStep({ serviceStep: "commitment" });
    }
  });
  const answered = commitments.every((c) => typeof c.answer !== "undefined");
  const onSubmitCommitmentsPress = useCallback(
    async (commitments: CostEstimationCommitment[]) => {
      await onSubmitCommitments(commitments);
      setStep({ serviceStep: "calculator" });
    },
    [onSubmitCommitments, setStep]
  );

  const onBackToCommitment = useCallback(() => {
    setStep({ serviceStep: "commitment" });
  }, [setStep]);

  const onFractionsWeightChange = useCallback(
    (fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[]) => {
      console.log("onFractionsWeightChange", fractions);
      onFractionsChange(packagingService.id, fractions);
    },
    [packagingService, onFractionsChange]
  );

  return (
    <div className="flex flex-col gap-10">
      <p className="text-base text-tonal-cream-30">{packagingService?.description}</p>
      {serviceStep === "commitment" || !answered ? (
        <CostEstimationServiceTypeAssessmentCommitment commitments={commitments} onSubmit={onSubmitCommitmentsPress} />
      ) : null}
      {serviceStep === "calculator" && answered ? (
        <CostEstimationServiceTypeAssessmentCalculator
          fractions={packagingService.fractions}
          onBack={onBackToCommitment}
          onChange={onFractionsWeightChange}
        />
      ) : null}
    </div>
  );
});
