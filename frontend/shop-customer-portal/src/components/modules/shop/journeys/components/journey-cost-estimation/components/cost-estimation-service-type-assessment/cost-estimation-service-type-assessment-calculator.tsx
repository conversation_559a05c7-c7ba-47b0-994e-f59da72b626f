"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { memo, useCallback } from "react";
import { CostEstimationServiceTypeAssessmentPackagingServiceFraction } from "../../common";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { FractionInput } from "@/components/_common/forms/fraction-input";
import { FractionIcon } from "@/components/_common/fraction-icon";

interface CostEstimationServiceTypeAssessmentCalculatorProps {
  onBack: () => void;
  fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[];
  onChange: (fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[]) => void;
}

export const CostEstimationServiceTypeAssessmentCalculator = memo(
  function CostEstimationServiceTypeAssessmentCalculator(props: CostEstimationServiceTypeAssessmentCalculatorProps) {
    const { onBack, fractions, onChange } = props;
    const onInputChange = useCallback(
      (id: number, qty: number) => {
        onChange(
          fractions.reduce(
            (fractions, fraction) => {
              let data = fraction;
              if (fraction.id === id) {
                data = { ...fraction, weight: qty };
              }
              return [...fractions, data];
            },
            [] as typeof fractions
          )
        );
      },
      [onChange, fractions]
    );
    return (
      <div className="-mt-4">
        <Button
          className="space-y-6 mb-4"
          color="light-blue"
          variant="text"
          size="small"
          onClick={onBack}
          type="button"
        >
          {"Edit your answers"}
        </Button>
        <p className="text-base text-tonal-cream-30">
          Please provide estimated quantities to help us calculate your future licensing costs.
        </p>
        {/* calculator */}
        <div className="mt-10 rounded-[20px] overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
          {Object.values(fractions).map((fraction) => (
            <CalculatorRow key={fraction.id} data={fraction} onChange={onInputChange} />
          ))}
        </div>
      </div>
    );
  }
);

interface CalculatorRowProps {
  data: CostEstimationServiceTypeAssessmentPackagingServiceFraction;
  onChange: (id: number, qty: number) => void;
}
const CalculatorRow = memo(function CalculatorRow(props: CalculatorRowProps) {
  const { data, onChange } = props;
  const onInputChange = useCallback(
    (qty: number | string) => {
      onChange(data.id, +qty);
    },
    [data, onChange]
  );
  return (
    <div className="bg-white" key={`fractionContainer-${data.name}`}>
      <div className="py-[14px] px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <QuestionTooltip>
            <QuestionTooltipTitle>
              <FractionIcon iconUrl={data.icon} size="small" className="size-3 md:size-6 fill-primary" />
              <p className="text-primary text-md font-bold">{data.name}</p>
            </QuestionTooltipTitle>
            <QuestionTooltipDescription className="text-primary">{data.description}</QuestionTooltipDescription>
          </QuestionTooltip>

          <div className="flex flex-1 items-center gap-3">
            <FractionIcon iconUrl={data.icon} size="medium" className="size-6 md:size-9 fill-primary" />
            <p className="text-sm md:text-base font-bold text-primary">{data.name}</p>
          </div>
        </div>
        <div className="flex items-center gap-4 w-full md:w-48 flex-shrink-0">
          <FractionInput
            type="weight"
            value={typeof data.weight === "number" ? data.weight : void 0}
            data-invalid={false}
            onChange={onInputChange}
            placeholder="0,00"
            className="fraction-input text-primary [&:not(:placeholder-shown)]:font-bold"
          />
          <span className="text-base text-primary">kg</span>
        </div>
      </div>
    </div>
  );
});
