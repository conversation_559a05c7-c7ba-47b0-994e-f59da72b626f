"use client";

import { useCallback } from "react";
import { CostEstimationMain } from "./journey-cost-estimation-main";
import { CostEstimationAssessment } from "./journey-cost-estimation-assessment";
import { useMockData } from "./mock";
import { CostEstimationResults } from "./journey-cost-estimation-results";
import { useSearchParamsSteps } from "./common";

export function JourneyCostEstimation() {
  const {
    countries,
    selectedCountry,
    setSelectedCountry,
    assessment,
    submitPackagingServicesCommitments,
    submitOtherCostsCommitments,
    onFractionsChange,
    results,
  } = useMockData();

  const {
    steps: [step],
    setStep,
  } = useSearchParamsSteps(["step", "country"], ([step, country]) => {
    const isValidStep = step && ["main", "assessment", "result"].includes(step);
    if (!isValidStep) {
      setStep({ step: "main" });
    } else {
      if (step === "assessment") {
        if (!country || !countries.find((c) => c.country_code === country)) {
          setStep({ step: "assessment", country: countries[0].country_code });
        }
      }
    }
  });

  const onToAssessment = useCallback(() => {
    setStep({ step: "assessment", country: selectedCountry });
  }, [setStep, selectedCountry]);

  const onToResult = useCallback(() => {
    setStep({ step: "result" });
  }, [setStep]);

  return (
    <>
      {step === "main" ? <CostEstimationMain onToAssessment={onToAssessment} /> : null}
      {step === "assessment" ? (
        <CostEstimationAssessment
          countries={countries}
          selectedCountry={selectedCountry}
          setSelectedCountry={setSelectedCountry}
          assessment={assessment}
          submitPackagingServicesCommitments={submitPackagingServicesCommitments}
          submitOtherCostsCommitments={submitOtherCostsCommitments}
          onFractionsChange={onFractionsChange}
          onToResult={onToResult}
        />
      ) : null}
      {step === "result" ? <CostEstimationResults data={results} onToAssessment={onToAssessment} /> : null}
    </>
  );
}
