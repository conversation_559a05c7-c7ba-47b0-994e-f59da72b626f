"use client";

import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { JourneyShoppingCartCard } from "@/components/modules/shop/journeys/components/journey-shopping-cart-card";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { memo, useCallback } from "react";
import {
  CostEstimationBanner,
  CostEstimationLayout,
  CostEstimationLayoutLeft,
  CostEstimationLayoutRight,
  CostEstimationNote,
} from "./common";

interface CostEstimationMainProps {
  onToAssessment: () => void;
}
export const CostEstimationMain = memo(function CostEstimationMain(props: CostEstimationMainProps) {
  const { onToAssessment } = props;
  const t = useTranslations("shop.longJourney.costEstimation");
  const onToInfo = useCallback(() => {
    location.href = "./informations";
  }, []);
  return (
    <CostEstimationLayout title={t("title")} description={t("main.description")}>
      <CostEstimationLayoutLeft>
        <CostEstimationNote note={t("main.note")} />
        <div className="p-10 bg-tonal-dark-cream-96 rounded-[40px]">
          <p className="text-primary text-2xl font-bold">{t("main.estimation.title")}</p>
          <p className="text-tonal-dark-cream-30 text-base mt-5">{t("main.estimation.description")}</p>
          <Button
            className="w-full mt-8"
            trailingIcon={<East />}
            variant="outlined"
            color="dark-blue"
            size="small"
            onClick={onToAssessment}
          >
            {t("main.estimation.btnLabel")}
          </Button>
        </div>
      </CostEstimationLayoutLeft>
      <CostEstimationLayoutRight>
        <CostEstimationBanner />
        <JourneyShoppingCartCard defaultLayout="mini" />
        <Button
          className="w-full"
          trailingIcon={<East />}
          color={"yellow"}
          variant="filled"
          size="medium"
          onClick={onToInfo}
          type="button"
        >
          {t("main.submitLabel")}
        </Button>
      </CostEstimationLayoutRight>
    </CostEstimationLayout>
  );
});
