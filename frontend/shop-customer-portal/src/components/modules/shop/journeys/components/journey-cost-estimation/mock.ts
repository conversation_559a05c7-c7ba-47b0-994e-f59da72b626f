import { useCallback, useEffect, useMemo, useState } from "react";
import {
  CostEstimationCommitment,
  CostEstimationServiceTypeAssessmentPackagingService,
  CostEstimationServiceTypeAssessmentPackagingServiceFraction,
  CostEstimationResult,
  CostEstimationResultItem,
  useMethod,
} from "./common";

// Types for hierarchical data structure
export interface CountryData {
  country_code: string;
  country_name: string;
  country_flag: string;
  packagingServices: PackagingServiceData[];
  packagingServicesCommitments: CostEstimationCommitment[];
  otherCostsCommitments: CostEstimationCommitment[];
  result: CostEstimationResult;
}

export interface PackagingServiceData {
  id: number;
  name: string;
  description: string;
  fractions: PackagingServiceFraction[];
}

export interface PackagingServiceFraction {
  id: number;
  code: string;
  name: string;
  description: string;
  price: number;
  unit: "kg";
  icon: string;
}

// Mock data for countries
export const mockCountries = [
  {
    country_code: "DE",
    country_name: "Germany",
    country_flag: "https://flagcdn.com/w80/de.png",
  },
  {
    country_code: "FR",
    country_name: "France",
    country_flag: "https://flagcdn.com/w80/fr.png",
  },
  {
    country_code: "IT",
    country_name: "Italy",
    country_flag: "https://flagcdn.com/w80/it.png",
  },
  {
    country_code: "ES",
    country_name: "Spain",
    country_flag: "https://flagcdn.com/w80/es.png",
  },
  {
    country_code: "NL",
    country_name: "Netherlands",
    country_flag: "https://flagcdn.com/w80/nl.png",
  },
];

// Data generation functions
export function generatePackagingServiceFractions(countryCode: string): PackagingServiceFraction[] {
  // Create unique numeric IDs based on country code
  const countryBaseId =
    {
      DE: 1000,
      FR: 2000,
      IT: 3000,
      ES: 4000,
      NL: 5000,
    }[countryCode] || 1000;

  const baseData = {
    plastic: {
      id: countryBaseId + 1,
      code: "PL",
      name: "Plastic Packaging",
      description: "All plastic packaging materials including bottles, containers, films, and wrapping",
    },
    glass: {
      id: countryBaseId + 2,
      code: "GL",
      name: "Glass Packaging",
      description: "Glass bottles, jars, and containers used for product packaging",
    },
    metal: {
      id: countryBaseId + 3,
      code: "MT",
      name: "Metal Packaging",
      description: "Aluminum cans, steel caps, and other metal packaging components",
    },
    paper: {
      id: countryBaseId + 4,
      code: "PP",
      name: "Paper & Cardboard",
      description: "Paper labels, cardboard boxes, and paper-based packaging materials",
    },
  };

  // Different pricing per country
  const countryPricing: Record<string, Record<string, number>> = {
    DE: { plastic: 0.85, glass: 0.12, metal: 0.45, paper: 0.25 },
    FR: { plastic: 0.92, glass: 0.15, metal: 0.48, paper: 0.28 },
    IT: { plastic: 0.78, glass: 0.1, metal: 0.42, paper: 0.22 },
    ES: { plastic: 0.73, glass: 0.09, metal: 0.38, paper: 0.2 },
    NL: { plastic: 0.88, glass: 0.14, metal: 0.46, paper: 0.26 },
  };

  const pricing = countryPricing[countryCode] || countryPricing.DE;

  return [
    {
      ...baseData.plastic,
      price: pricing.plastic,
      unit: "kg" as const,
      icon: `https://flagcdn.com/w80/${countryCode.toLowerCase()}.png`,
    },
    {
      ...baseData.glass,
      price: pricing.glass,
      unit: "kg" as const,
      icon: `https://flagcdn.com/w80/${countryCode.toLowerCase()}.png`,
    },
    {
      ...baseData.metal,
      price: pricing.metal,
      unit: "kg" as const,
      icon: `https://flagcdn.com/w80/${countryCode.toLowerCase()}.png`,
    },
    {
      ...baseData.paper,
      price: pricing.paper,
      unit: "kg" as const,
      icon: `https://flagcdn.com/w80/${countryCode.toLowerCase()}.png`,
    },
  ];
}

export function generatePackagingServices(countryCode: string): PackagingServiceData[] {
  const baseFractions = generatePackagingServiceFractions(countryCode);

  // Different countries have different packaging services
  const countryServices: Record<string, PackagingServiceData[]> = {
    DE: [
      {
        id: 100, // DE base + 0
        name: "Sales Packaging",
        description:
          "Sales Packaging is the packaging that directly contains the product and is typically designed to be handed over to the end consumer.",
        fractions: baseFractions,
      },
      {
        id: 101, // DE base + 1
        name: "Transport Packaging",
        description: "Transport packaging is used to protect goods during transportation and storage.",
        fractions: baseFractions.map((fraction) => ({
          ...fraction,
          id: fraction.id + 100, // Add 100 to base ID for transport variants
          price: fraction.price * 0.8,
        })),
      },
    ],
    FR: [
      {
        id: 200, // FR base + 0
        name: "Sales Packaging",
        description: "Sales Packaging is the packaging that directly contains the product.",
        fractions: baseFractions,
      },
      {
        id: 201, // FR base + 1
        name: "Transport Packaging",
        description: "Transport packaging for protection during transport.",
        fractions: baseFractions.map((fraction) => ({
          ...fraction,
          id: fraction.id + 100, // Add 100 to base ID for transport variants
          price: fraction.price * 0.85,
        })),
      },
    ],
    IT: [
      {
        id: 300, // IT base + 0
        name: "Consumer Packaging",
        description: "Consumer packaging subject to Italian recycling regulations.",
        fractions: baseFractions,
      },
      {
        id: 301, // IT base + 1
        name: "Export Packaging",
        description: "Specialized packaging for export with additional protection.",
        fractions: baseFractions.map((fraction) => ({
          ...fraction,
          id: fraction.id + 100, // Add 100 to base ID for export variants
          price: fraction.price * 1.1,
        })),
      },
    ],
    ES: [
      {
        id: 400, // ES base + 0
        name: "Consumer Packaging",
        description: "Consumer packaging for Spanish market.",
        fractions: baseFractions.filter((f) => f.code !== "GL"),
      },
    ],
    NL: [
      {
        id: 500, // NL base + 0
        name: "Sales Packaging",
        description: "Sales packaging for Dutch market.",
        fractions: baseFractions,
      },
      {
        id: 501, // NL base + 1
        name: "Reusable Packaging",
        description: "Reusable packaging with return obligations.",
        fractions: baseFractions
          .filter((f) => ["GL", "MT"].includes(f.code))
          .map((fraction) => ({
            ...fraction,
            id: fraction.id + 100, // Add 100 to base ID for reusable variants
            price: fraction.price * 0.6,
          })),
      },
    ],
  };

  return countryServices[countryCode] || countryServices.DE;
}

export function generatePackagingServicesCommitments(countryCode: string): CostEstimationCommitment[] {
  const services = generatePackagingServices(countryCode);
  let commitmentId =
    100000 +
    (countryCode === "DE"
      ? 0
      : countryCode === "FR"
        ? 10000
        : countryCode === "IT"
          ? 20000
          : countryCode === "ES"
            ? 30000
            : 40000);

  const commitments: CostEstimationCommitment[] = [];

  services.forEach((service, serviceIndex) => {
    // For each service, create specific commitments
    commitments.push(
      {
        id: commitmentId++,
        title: `Do you regularly use "${service.name}" service?`,
        help_text: `${service.description} Please indicate if you use this service regularly.`,
        input_type: "YES_NO" as const,
        options: [
          { id: 1, option_value: "YES", option_to_value: null, value: "YES" },
          { id: 2, option_value: "NO", option_to_value: null, value: "NO" },
        ],
      },
      {
        id: commitmentId++,
        title: `What is your monthly volume for "${service.name}"?`,
        help_text: `Please estimate your typical monthly volume for ${service.name.toLowerCase()}.`,
        input_type: "SELECT" as const,
        options: [
          { id: 1, option_value: "LOW", option_to_value: "< 100kg", value: "LOW" },
          { id: 2, option_value: "MEDIUM", option_to_value: "100-1000kg", value: "MEDIUM" },
          { id: 3, option_value: "HIGH", option_to_value: "> 1000kg", value: "HIGH" },
        ],
      }
    );

    // Add service-specific questions based on service name
    if (service.name.includes("Sales")) {
      commitments.push({
        id: commitmentId++,
        title: `Do you have B2C sales for "${service.name}"?`,
        help_text: "Business-to-consumer sales may have different packaging requirements.",
        input_type: "YES_NO" as const,
        options: [
          { id: 1, option_value: "YES", option_to_value: null, value: "YES" },
          { id: 2, option_value: "NO", option_to_value: null, value: "NO" },
        ],
      });
    }

    if (service.name.includes("Transport")) {
      commitments.push({
        id: commitmentId++,
        title: `Do you use returnable transport packaging for "${service.name}"?`,
        help_text: "Returnable transport packaging may have different fee structures.",
        input_type: "YES_NO" as const,
        options: [
          { id: 1, option_value: "YES", option_to_value: null, value: "YES" },
          { id: 2, option_value: "NO", option_to_value: null, value: "NO" },
        ],
      });
    }

    if (service.name.includes("Reusable")) {
      commitments.push({
        id: commitmentId++,
        title: `What is the return rate for "${service.name}"?`,
        help_text: "Return rate affects the packaging fees and environmental impact.",
        input_type: "SELECT" as const,
        options: [
          { id: 1, option_value: "LOW", option_to_value: "< 30%", value: "LOW" },
          { id: 2, option_value: "MEDIUM", option_to_value: "30-70%", value: "MEDIUM" },
          { id: 3, option_value: "HIGH", option_to_value: "> 70%", value: "HIGH" },
        ],
      });
    }

    if (service.name.includes("Export")) {
      commitments.push({
        id: commitmentId++,
        title: `Do you export "${service.name}" outside EU?`,
        help_text: "Export outside EU may have different packaging regulations.",
        input_type: "YES_NO" as const,
        options: [
          { id: 1, option_value: "YES", option_to_value: null, value: "YES" },
          { id: 2, option_value: "NO", option_to_value: null, value: "NO" },
        ],
      });
    }
  });

  return commitments;
}
let _id = 0;
function id() {
  return ++_id;
}
export function generateOtherCostsCommitments(countryCode: string): CostEstimationCommitment[] {
  // Create unique numeric IDs based on country code for other costs commitments
  const baseCommitments = [
    {
      id: id(),
      title: "Do you have administrative costs for compliance?",
      help_text: "This includes costs for regulatory compliance, reporting, and administrative overhead.",
      input_type: "YES_NO" as const,
      options: [
        { id: id(), option_value: "YES", option_to_value: null, value: "YES" },
        { id: id(), option_value: "NO", option_to_value: null, value: "NO" },
      ],
    },
    {
      id: id(),
      title: "What type of certification costs do you have?",
      help_text: "Select all applicable certification and audit costs.",
      input_type: "SELECT" as const,
      options: [
        { id: id(), option_value: "iso", option_to_value: null, value: "ISO Certifications" },
        { id: id(), option_value: "eco", option_to_value: null, value: "Eco-labels" },
        { id: id(), option_value: "quality", option_to_value: null, value: "Quality Assurance" },
      ],
    },
  ];

  // Add country-specific other costs
  const countrySpecificCosts: Record<string, CostEstimationCommitment[]> = {
    DE: [
      {
        id: id(),
        title: "Do you have DSD (Duales System Deutschland) fees?",
        help_text: "Fees paid to the German dual system for packaging waste management.",
        input_type: "YES_NO" as const,
        options: [
          { id: id(), option_value: "YES", option_to_value: null, value: "YES" },
          { id: id(), option_value: "NO", option_to_value: null, value: "NO" },
        ],
      },
    ],
    FR: [
      {
        id: id(),
        title: "Do you pay eco-contribution fees (Citeo)?",
        help_text: "Fees paid to French packaging recovery organization.",
        input_type: "YES_NO" as const,
        options: [
          { id: id(), option_value: "YES", option_to_value: null, value: "YES" },
          { id: id(), option_value: "NO", option_to_value: null, value: "NO" },
        ],
      },
    ],
  };

  return [...baseCommitments, ...(countrySpecificCosts[countryCode] || [])];
}

export function generateCountryData(countryCode: string): CountryData {
  const country = mockCountries.find((c) => c.country_code === countryCode);
  if (!country) {
    throw new Error(`Country with code ${countryCode} not found`);
  }
  const packagingServices = generatePackagingServices(countryCode);
  return {
    ...country,
    packagingServices: packagingServices,
    packagingServicesCommitments: generatePackagingServicesCommitments(countryCode),
    otherCostsCommitments: generateOtherCostsCommitments(countryCode),
    result: {
      country: country,
      items: packagingServices.map((sreviceType) => {
        return {
          type: "SERVICE_TYPE",
          id: sreviceType.id,
          price: 0,
          name: sreviceType.name,
          furtherInformationRequired: true,
        } as CostEstimationResultItem;
      }),
    },
  };
}

export function generateAllCountriesData(): CountryData[] {
  return mockCountries.map((country) => generateCountryData(country.country_code));
}

// Backward compatibility - these are used by existing components
// Mock data for service type assessment
export const mockCommitments: CostEstimationCommitment[] = generatePackagingServicesCommitments("DE");

export const mockCostCommitments: CostEstimationCommitment[] = generateOtherCostsCommitments("DE");

export const mockPackagingService: CostEstimationServiceTypeAssessmentPackagingService = {
  id: 1,
  name: "Sales Packaging",
  description:
    "Sales Packaging is the packaging that directly contains the product and is typically designed to be handed over to the end consumer. Sales Packaging is often subject to licensing and recycling obligations.",
  fractions: generatePackagingServiceFractions("DE").map((fraction) => ({
    ...fraction,
    weight: undefined, // Optional field for backward compatibility
  })),
};

// Helper functions to get country-specific data
export function getCountryPackagingServices(
  countryCode: string
): CostEstimationServiceTypeAssessmentPackagingService[] {
  return generatePackagingServices(countryCode).map((service) => ({
    ...service,
    fractions: service.fractions.map((fraction) => ({ ...fraction, weight: undefined })),
  }));
}

export function getCountryPackagingServicesCommitments(countryCode: string): CostEstimationCommitment[] {
  return generatePackagingServicesCommitments(countryCode);
}

export function getCountryOtherCostsCommitments(countryCode: string): CostEstimationCommitment[] {
  return generateOtherCostsCommitments(countryCode);
}

///
const store = (() => {
  const datas: CountryData[] = mockCountries.map((country) => generateCountryData(country.country_code));
  function get(code: string) {
    return datas.find((c) => c.country_code === code) as CountryData;
  }
  function set(code: string, data: CountryData) {
    const idx = datas.findIndex((c) => c.country_code === code);
    datas[idx] = { ...data };
  }

  return {
    get,
    set,
    datas,
  };
})();

export function useMockData() {
  const [selectedCountry, _setSelectedCountry] = useState<string>(mockCountries[0].country_code);
  const setSelectedCountry = useMethod((country: string) => {
    if (!mockCountries.find((c) => c.country_code === country)) {
      _setSelectedCountry(selectedCountry); // set to prev country
      return;
    }
    _setSelectedCountry(country);
  });
  const [assessment, setAssessment] = useState(store.get(selectedCountry));
  useEffect(() => {
    setAssessment(store.get(selectedCountry));
  }, [selectedCountry]);

  const submitPackagingServicesCommitments = useCallback(
    async (commitments: CostEstimationCommitment[]) => {
      const newData = {
        ...assessment,
        packagingServicesCommitments: assessment.packagingServicesCommitments.reduce(
          (all, item) => {
            const updatedItem = commitments.find((c) => c.id === item.id);
            if (updatedItem) {
              return [...all, updatedItem];
            }
            return [...all, item];
          },
          [] as typeof assessment.packagingServicesCommitments
        ),
      };
      store.set(selectedCountry, newData);
      setAssessment(store.get(selectedCountry));
    },
    [selectedCountry, assessment]
  );
  const submitOtherCostsCommitments = useCallback(
    async (commitments: CostEstimationCommitment[]) => {
      const newData = {
        ...assessment,
        otherCostsCommitments: assessment.otherCostsCommitments.reduce(
          (all, item) => {
            const updatedItem = commitments.find((c) => c.id === item.id);
            if (updatedItem) {
              return [...all, updatedItem];
            }
            return [...all, item];
          },
          [] as typeof assessment.otherCostsCommitments
        ),
      };

      // result
      newData.result.items = [
        ...newData.result.items.filter((it) => it.type === "SERVICE_TYPE"),
        ...newData.otherCostsCommitments.reduce((items, otherCostsCommitment, i) => {
          if (otherCostsCommitment.answer) {
            const idx = otherCostsCommitment.options.findIndex((o) => o.value === otherCostsCommitment.answer);
            if (idx === 0) {
              items.push({
                id: 90000 + i,
                type: "OTHER_COSTS",
                name: `Cost-${i + 1}`,
                price: i + 1 * 100,
                furtherInformationRequired: false,
              });
            }
          }
          return items;
        }, [] as CostEstimationResultItem[]),
      ];
      store.set(selectedCountry, newData);
      setAssessment(store.get(selectedCountry));
    },
    [selectedCountry, assessment]
  );
  const onFractionsChange = useCallback(
    async (packagingServicesId: number, fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[]) => {
      const newData = {
        ...assessment,
        packagingServices: assessment.packagingServices.map((serviceType) => {
          if (serviceType.id === packagingServicesId) {
            return {
              ...serviceType,
              fractions,
            };
          }
          return serviceType;
        }),
      };
      const serviceType = newData.packagingServices.find((p) => p.id === packagingServicesId);
      if (!serviceType) {
        return;
      }
      // result
      const itemIdx = newData.result.items.findIndex((it) => it.id === serviceType.id);
      newData.result.items[itemIdx] = fractions.reduce(
        (result, item) => {
          result.price = (result.price || 0) + item.price * (item.weight || 0);
          if (typeof item.weight !== "number") {
            result.furtherInformationRequired = true;
          }
          return result;
        },
        {
          ...newData.result.items[itemIdx],
          price: 0,
          furtherInformationRequired: false as boolean,
        }
      );

      store.set(selectedCountry, newData);
      setAssessment(store.get(selectedCountry));
    },
    [selectedCountry, assessment]
  );

  return {
    countries: mockCountries,
    selectedCountry,
    setSelectedCountry,
    assessment,
    submitPackagingServicesCommitments,
    submitOtherCostsCommitments,
    onFractionsChange,
    results: useMemo(() => {
      return store.datas.map((c) => c.result);
    }, [assessment]),
  };
}

export type CostEstimationMock = ReturnType<typeof useMockData>;
