"use client";

import { RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { memo, useCallback } from "react";
import { CostEstimationCommitment } from "../../common";

interface CostEstimationAssessmentCommitmentQuestionProps {
  commitment: CostEstimationCommitment;
  onChange: (commitment: CostEstimationCommitment) => void;
}
export const CostEstimationAssessmentCommitmentQuestion = memo(function CostEstimationAssessmentCommitmentQuestion(
  props: CostEstimationAssessmentCommitmentQuestionProps
) {
  const { commitment, onChange } = props;
  const onChecked = useCallback(
    (option: CostEstimationCommitment["options"][number]) => {
      onChange({
        ...commitment,
        answer: option.value,
      });
    },
    [commitment, onChange]
  );
  return (
    <div className="flex flex-col gap-4">
      <p id={`${commitment.id}-title`} className="text-base text-primary">
        {commitment.title}
      </p>
      <div className="flex flex-row flex-wrap gap-8" role="radiogroup" aria-labelledby={`${commitment.id}-title`}>
        {commitment.options.map((option) => {
          return (
            <CostEstimationAssessmentCommitmentQuestionOption
              key={option.id}
              commitmentId={commitment.id}
              type={commitment.input_type}
              option={option}
              onChecked={onChecked}
              checked={commitment.answer === option.value}
            />
          );
        })}
      </div>
    </div>
  );
});

interface CostEstimationAssessmentCommitmentQuestionOptionProps {
  commitmentId: number;
  type: CostEstimationCommitment["input_type"];
  option: CostEstimationCommitment["options"][number];
  onChecked: (option: CostEstimationCommitment["options"][number]) => void;
  checked: boolean;
}

const CostEstimationAssessmentCommitmentQuestionOption = memo(function CostEstimationAssessmentCommitmentQuestionOption(
  props: CostEstimationAssessmentCommitmentQuestionOptionProps
) {
  const { commitmentId, type, option, onChecked, checked } = props;
  const onChange = useCallback(() => {
    onChecked(option);
  }, [onChecked, option]);
  const globalT = useTranslations("global");
  const id = `${commitmentId}-${option.id}`;
  return (
    <div>
      <label
        className="flex flex-row gap-2 text-base text-primary items-center cursor-pointer rounded-md p-1 has-[:focus-visible]:ring-2 has-[:focus-visible]:ring-primary has-[:focus-visible]:ring-offset-2"
        htmlFor={id}
      >
        <input
          className="sr-only peer"
          type="radio"
          id={id}
          name={`commitment-${commitmentId}`}
          value={option.value}
          checked={checked}
          onChange={onChange}
        />
        <RadioSelected className="hidden peer-checked:block size-5 fill-primary" aria-hidden="true" />
        <RadioUnselected className="block peer-checked:hidden size-5 fill-primary" aria-hidden="true" />
        {type === "YES_NO"
          ? option.option_value === "YES"
            ? globalT("inputs.yesOrNo.yes")
            : globalT("inputs.yesOrNo.no")
          : null}
        {type === "SELECT" ? option.option_value : null}
      </label>
    </div>
  );
});
