import { useCustomer } from "@/hooks/use-customer";
import { useRouter } from "@/i18n/navigation";
import { createCompany, updateCompany, validateVatId } from "@/lib/api/company";
import { updateCustomer } from "@/lib/api/customer";
import { createPaymentCustomer } from "@/lib/api/payment";
import { joinStrings } from "@/utils/join-strings";
import { validateLUCIDNumber } from "@/utils/validateLucidNumber";
import { useVatErrorTranslator } from "@/utils/vat-error-translator";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useFormContext } from "react-hook-form";
import { JourneyInformationFormAccount } from "./journey-information-form-account";
import { JourneyInformationFormCompany } from "./journey-information-form-company";
import { JourneyInformationFormLucid } from "./journey-information-form-lucid";
import { JourneyInformationFormPersonal } from "./journey-information-form-personal";
import { JourneyInformationFormData } from "./journey-information-form-provider";

interface JourneyInformationFormProps {
  isDirectLicense?: boolean;
}

export function JourneyInformationForm({ isDirectLicense = false }: JourneyInformationFormProps) {
  const router = useRouter();
  const globalT = useTranslations("global");
  const getVatApiErrorMessage = useVatErrorTranslator();

  const { customer, invalidateCustomer } = useCustomer();
  const { handleSubmit, setError, trigger } = useFormContext<JourneyInformationFormData>();

  if (!customer) return null;

  async function handleFormSubmit(data: JourneyInformationFormData) {
    if (!customer) return;

    await trigger();

    if (!!data.phones.length) {
      data.phones.forEach((phone, index) => {
        if (!phone.phone_number.trim()) {
          setError(`phones.${index}`, { type: "manual", message: globalT("validation.required") });
          return;
        }
      });
    }

    if (!!data.emails.length) {
      data.emails.forEach((email, index) => {
        if (!email.email.trim()) {
          setError(`emails.${index}`, { type: "manual", message: globalT("validation.required") });
          return;
        }
      });
    }

    if (!data.city) {
      setError("city", { type: "manual", message: globalT("validation.required") });
      return;
    }

    if (data.documentType === "VAT" && !data.vatId) {
      setError("vatId", { type: "manual", message: globalT("validation.required") });
      return;
    }

    if (data.documentType === "TAX" && !data.taxNumber) {
      setError("taxNumber", { type: "manual", message: globalT("validation.required") });
      return;
    }

    if (isDirectLicense && data.lucidNumber?.length !== 0) {
      const isLUCIDNumberValid = validateLUCIDNumber(data.lucidNumber!);

      if (!isLUCIDNumberValid) {
        setError("lucidNumber", { type: "manual", message: globalT("inputs.lucid.errors.invalid") });
        return;
      }
    }

    try {
      if (data.documentType === "VAT" && data.vatId) {
        const vatValidationResponse = await validateVatId({
          vat_id: data.vatId,
          country_code: data.countryCode,
          company_name: data.companyName,
          company_zipcode: data.zipCode,
          company_city: data.city,
          company_street: data.streetAndNumber,
        });

        if (!vatValidationResponse?.is_valid) {
          const errorMessage = vatValidationResponse?.error
            ? getVatApiErrorMessage(vatValidationResponse.error)
            : globalT("inputs.vatId.errors.invalid");

          setError("vatId", {
            type: "manual",
            message: errorMessage,
          });
          throw new Error(errorMessage);
        }
      }

      let companyId = data.companyId;

      const companyResponse = companyId
        ? await updateCompany(companyId, {
            name: data.companyName,
            vat: data.vatId,
            tin: data.taxNumber,
            lucid: data.lucidNumber,
            emails: data.emails.map((email) => email.email),
            address: {
              country_code: data.countryCode,
              address_line: joinStrings([data.streetAndNumber, data.city, data.countryCode, data.zipCode]),
              city: data.city,
              zip_code: data.zipCode,
              street_and_number: data.streetAndNumber,
              additional_address: data.additionalAddressLine || "",
            },
          })
        : await createCompany({
            customer_id: customer.id,
            name: data.companyName,
            vat: data.vatId,
            tin: data.taxNumber,
            lucid: data.lucidNumber,
            emails: data.emails.map((email) => email.email),
            address: {
              country_code: data.countryCode,
              address_line: joinStrings([data.streetAndNumber, data.city, data.countryCode, data.zipCode]),
              city: data.city,
              zip_code: data.zipCode,
              street_and_number: data.streetAndNumber,
              additional_address: data.additionalAddressLine || "",
            },
          });

      if (!companyResponse.success) {
        if (companyResponse.type === "VAT_IN_USE") {
          setError("vatId", {
            type: "manual",
            message: globalT("inputs.vatId.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "TAX_IN_USE") {
          setError("taxNumber", {
            type: "manual",
            message: globalT("inputs.taxNumber.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "DOCUMENT_IN_USE") {
          setError("companyName", {
            type: "manual",
            message: globalT("inputs.companyName.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "LUCID_IN_USE") {
          setError("lucidNumber", {
            type: "manual",
            message: globalT("inputs.lucid.errors.alreadyRegistered"),
          });
          return;
        }

        return;
      }

      if (!companyId) companyId = companyResponse.data.id;

      await createPaymentCustomer(customer.id);

      await updateCustomer(customer.id!, {
        first_name: data.firstName,
        last_name: data.surname,
        salutation: data.salutation,
        phones: [data.phone, data.mobile || "", ...data.phones.map((phone) => phone.phone_number)],
        currency: "EUR",
      });

      invalidateCustomer();

      router.push("./billing");
    } catch (error: any) {
      enqueueSnackbar(error.message || globalT("inputs.vatId.errors.invalid"), { variant: "error" });
    }
  }

  return (
    <form id="journey-information-form" onSubmit={handleSubmit(handleFormSubmit)}>
      <div className="flex flex-col gap-6">
        <JourneyInformationFormCompany />
        <JourneyInformationFormPersonal />
        <JourneyInformationFormAccount />
        {isDirectLicense && <JourneyInformationFormLucid />}
      </div>
    </form>
  );
}
