"use client";

import {
  additionalAddressLine,
  city,
  companyId,
  companyName,
  confirmPassword,
  countryCode,
  documentType,
  email,
  emails,
  firstName,
  lucidNumber,
  mobile,
  password,
  phone,
  phones,
  salutation,
  streetAndNumber,
  surname,
  taxNumber,
  vatId,
  zipCode,
} from "@/components/_common/forms/schemas";
import { useCustomer } from "@/hooks/use-customer";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { ReactNode, useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { z } from "zod";

const journeyInformationFormSchema = z.object({
  companyId,
  companyName,
  countryCode,
  city,
  zipCode,
  streetAndNumber,
  additionalAddressLine,
  documentType,
  vatId,
  taxNumber,
  salutation,
  firstName,
  surname,
  phone,
  mobile,
  emails,
  lucidNumber,
  phones,
  email,
  password: password.optional(),
  confirmPassword: confirmPassword.optional(),
});

export type JourneyInformationFormData = z.infer<typeof journeyInformationFormSchema>;

interface JourneyInformationFormProviderProps {
  children: ReactNode;
}

export function JourneyInformationFormProvider({ children }: JourneyInformationFormProviderProps) {
  const { customer } = useCustomer();
  const t = useTranslations("shop.common.journey.information");

  const methods = useForm<JourneyInformationFormData>({
    resolver: zodResolver(journeyInformationFormSchema),
    mode: "all",
  });

  useEffect(() => {
    if (!customer) return;

    const customerCompany = customer.company;

    (async () => {
      methods.reset({
        firstName: customer.first_name,
        surname: customer.last_name,
        companyId: customer.company?.id || undefined,
        companyName: customerCompany?.name || undefined,
        countryCode: customerCompany?.address.country_code,
        city: customerCompany?.address.city,
        zipCode: customerCompany?.address.zip_code,
        streetAndNumber: customerCompany?.address.street_and_number,
        additionalAddressLine: customerCompany?.address.additional_address,
        documentType: customerCompany?.vat ? "VAT" : "TAX",
        vatId: customerCompany?.vat || undefined,
        taxNumber: customerCompany?.tin || undefined,
        lucidNumber: customerCompany?.lucid || undefined,
        salutation: customer.salutation || t("personal.salutation.masculine"),
        phones: customer.phones?.slice(2, customer.phones?.length).map((phone) => ({
          id: String(phone.id),
          phone_number: phone.phone_number,
        })),
        phone: customer.phones?.[0]?.phone_number,
        mobile: customer.phones?.[1]?.phone_number,
        emails: Array.isArray(customerCompany?.emails)
          ? customerCompany.emails.map((email) => (typeof email === "string" ? { email } : email))
          : [],
        email: "<EMAIL>", // TODO: Mock data for testing
        // email: (customer as any).email || "",
        password: undefined,
        confirmPassword: undefined,
      });
    })();
  }, [customer, customer?.company?.id]);

  return <FormProvider {...methods}>{children}</FormProvider>;
}
