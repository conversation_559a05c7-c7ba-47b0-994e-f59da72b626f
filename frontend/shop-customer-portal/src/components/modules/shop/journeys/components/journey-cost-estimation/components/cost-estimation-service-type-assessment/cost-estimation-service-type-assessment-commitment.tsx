"use client";

import { useTranslations } from "next-intl";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { CostEstimationCommitment } from "../../common";
import { CostEstimationAssessmentCommitmentQuestion } from "../cost-estimation-assessment-commitment";

interface CostEstimationServiceTypeAssessmentCommitmentProps {
  commitments: CostEstimationCommitment[];
  onSubmit: (commitments: CostEstimationCommitment[]) => void;
}
export const CostEstimationServiceTypeAssessmentCommitment = memo(
  function CostEstimationServiceTypeAssessmentCommitment(props: CostEstimationServiceTypeAssessmentCommitmentProps) {
    const { commitments, onSubmit } = props;
    const [data, setData] = useState(commitments || []);

    useEffect(() => {
      setData(commitments);
    }, [commitments]);

    const onChange = useCallback((target: CostEstimationCommitment) => {
      setData((data) =>
        data.reduce(
          (data, commitment) => [...data, commitment.id === target.id ? target : commitment],
          [] as typeof data
        )
      );
    }, []);
    const onContinue = useCallback(() => {
      onSubmit(data);
    }, [data, onSubmit]);
    const canContinue = useMemo(() => data.every((a) => typeof a.answer !== "undefined"), [data]);
    const t = useTranslations("shop.longJourney.costEstimation");
    return (
      <div className="flex flex-col gap-10">
        {data.map((commitment) => {
          return (
            <CostEstimationAssessmentCommitmentQuestion
              key={commitment.id}
              commitment={commitment}
              onChange={onChange}
            />
          );
        })}
        <Button
          className="self-start"
          color="dark-blue"
          variant="filled"
          size="medium"
          onClick={onContinue}
          type="button"
          disabled={!canContinue}
        >
          {t("assessment.estimation.commitment.submitLabel")}
        </Button>
      </div>
    );
  }
);
