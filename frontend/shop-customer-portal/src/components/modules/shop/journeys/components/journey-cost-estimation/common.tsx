"use client";

import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { Add, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { memo, PropsWithChildren, useEffect, useMemo, useRef } from "react";
import { CountryIcon } from "@/components/_common/country-icon";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link, usePathname, useRouter } from "@/i18n/navigation";
import { IconBanner, ShopBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Icons } from "@/components/ui/icons";
import { useSearchParams } from "next/navigation";

/**
 * MARK: types
 */
export interface CountryEssential {
  country_code: string;
  country_name: string;
  country_flag: string;
}

export interface CostEstimationServiceTypeAssessmentPackagingService {
  id: number;
  name: string;
  description: string;

  /**
   * decided after customer answer questions
   */
  fractions: CostEstimationServiceTypeAssessmentPackagingServiceFraction[];
}

export interface CostEstimationServiceTypeAssessmentPackagingServiceFraction {
  id: number;
  code: string;
  name: string;
  icon: string; // icon uri
  description: string;
  price: number;
  weight?: number;
  unit: "kg";
}
export interface CostEstimationCommitment {
  id: number;
  title: string;
  help_text: string | null;
  input_type: "YES_NO" | "SELECT";
  options: {
    id: number;
    option_value: string;
    option_to_value: string | null;
    value: string;
  }[];
  answer?: string;
}

export interface CostEstimationResultItem {
  /**
   * id for srevice type or other costs
   */
  id: number;
  type: "SERVICE_TYPE" | "OTHER_COSTS";
  name: string;
  price?: number;
  furtherInformationRequired?: boolean;
}
export interface CostEstimationResult {
  country: CountryEssential;
  items: CostEstimationResultItem[];
}

/**
 * MARK: Components
 */
interface CostEstimationLayoutProps {
  children: [React.ReactNode, React.ReactNode];
  header?: React.ReactNode;
  title: string;
  description: string;
}
export const CostEstimationLayout = memo(function CostEstimationLayout(props: CostEstimationLayoutProps) {
  const { children, title, description, header } = props;
  const [left, right] = children;
  const t = useTranslations("shop.longJourney.costEstimation");
  return (
    <>
      {/* header */}
      <div>
        <div className="flex-1 flex flex-col md:flex-row gap-6 w-full justify-between">
          {/* left */}
          <div className={cn("pb-8 md:pb-16 max-w-screen-md")}>
            <div className=" flex items-center gap-4">
              <div className="flex flex-row items-center gap-2">
                <p className={`text-primary md:text-5xl text-3xl font-semibold md:leading-[62.4px]`}>{title}</p>
                <p className={`text-primary md:text-3xl text-xl md:leading-[62.4px]`}>{t("optional")}</p>
              </div>
            </div>
            <p className="text-primary pr-32">{description}</p>
          </div>
          {/* right */}
          <div className="flex flex-col gap-10 w-full md:max-w-[40%] flex-1"></div>
        </div>
        {header}
        {header ? <hr className="text-tonal-dark-cream-80 w-full justify-start my-6" /> : null}
      </div>
      {/* content */}
      <div className="pb-10 md:pb-32">
        <div className="flex flex-col md:flex-row gap-6 w-full justify-between">
          {/* Left */}
          <div className="flex flex-col gap-6 flex-1">{left}</div>
          {/* Right */}
          <div className="flex flex-col gap-10 w-full md:max-w-[40%] flex-1">{right}</div>
        </div>
      </div>
    </>
  );
});
type CostEstimationLayoutLeftProps = PropsWithChildren;
export const CostEstimationLayoutLeft = memo(function CostEstimationLayoutLeft({
  children,
}: CostEstimationLayoutLeftProps) {
  return <>{children}</>;
});
type CostEstimationLayoutRightProps = PropsWithChildren;
export const CostEstimationLayoutRight = memo(function CostEstimationLayoutRight({
  children,
}: CostEstimationLayoutRightProps) {
  return <>{children}</>;
});

interface CostEstimationNoteProps {
  note: string;
}
export const CostEstimationNote = memo(function CostEstimationNote(props: CostEstimationNoteProps) {
  const { note } = props;
  return (
    <div>
      <div className="p-4 bg-surface-01 rounded-[20px] flex flex-row items-center gap-3">
        <Error className="fill-primary size-6" />
        <p className="text-primary text-base leading-5 flex-1">{note}</p>
      </div>
    </div>
  );
});

interface ShopCountryTabsProps {
  selectedCountry: string | null;
  onCountrySelect: (countryCode: string) => void;
  className?: string;
  countries: CountryEssential[];
}
export const CostEstimationCountryTabs = memo(function CostEstimationCountryTabs(props: ShopCountryTabsProps) {
  const { selectedCountry, onCountrySelect, className, countries } = props;

  const handleValueChange = (value: string) => {
    onCountrySelect(value);
  };

  return (
    <div className={className}>
      <Tabs value={selectedCountry || ""} onValueChange={handleValueChange}>
        <div className="flex flex-wrap items-center gap-5">
          <TabsList className="flex-wrap gap-5 justify-start">
            {countries.map((country) => {
              const isActive = selectedCountry === country.country_code;
              return (
                <TabsTrigger
                  key={country.country_code}
                  value={country.country_code}
                  className="gap-3 bg-tonal-dark-blue-96"
                >
                  <CountryIcon
                    country={{
                      flag_url: country.country_flag,
                      name: country.country_name,
                    }}
                    className={cn("size-6", isActive && "border rounded-3xl")}
                  />
                  {country.country_name}
                </TabsTrigger>
              );
            })}
            <Link
              href="/eu/long-journey/select-countries"
              aria-label="Add country"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-full px-5 py-1 text-lg font-bold text-primary gap-2 border-primary border-2 bg-transparent hover:bg-gray-50"
            >
              <Add className="size-5 fill-primary" /> <span className="text-base">Add</span>
            </Link>
          </TabsList>
        </div>
      </Tabs>
    </div>
  );
});

export const CostEstimationBanner = memo(function CostEstimationBanner() {
  const t = useTranslations("shop.longJourney.costEstimation");
  return (
    <ShopBanner className="bg-tonal-dark-blue-96 !w-full py-12 px-8" title="">
      <div className="flex flex-col gap-10">
        <div className="flex items-start">
          <IconBanner className="text-white " icon={() => <Icons.lock className="fill-tonal-dark-blue-80" />} />

          <div>
            <p className="font-bold text-base">{t("banners.first.title")}</p>
            <span className="w-full text-sm ">{t("banners.first.description")}</span>
          </div>
        </div>
      </div>
    </ShopBanner>
  );
});
export function useMethod<T extends (...args: any[]) => any>(callback: T) {
  const fn = useRef(callback);
  useEffect(() => {
    fn.current = callback;
  }, [callback]);

  return useMemo(() => {
    return (...args: any[]) => {
      return fn.current(...args);
    };
  }, []) as T;
}

export type CostEstimationStep = "main" | "assessment" | "result";
export type CostEstimationServiceTypeStep = "commitment" | "calculator";

interface SearchParamsSteps {
  step: CostEstimationStep;

  /**
   * Only after step "assessment"
   */
  country: string;

  accordionOpen: string;

  serviceStep: CostEstimationServiceTypeStep;
  otherCostsCompleted: "true";
}

type SearchParamsStepsKey = keyof SearchParamsSteps;
type SearchParamsStepsValue = SearchParamsSteps[keyof SearchParamsSteps];

export function useSearchParamsSteps(
  watch?: SearchParamsStepsKey[],
  onStep?: (steps: SearchParamsStepsValue[]) => void
) {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const updateSearchParams = useMethod((newParams: Partial<SearchParamsSteps>) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(newParams).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  });

  const steps = useMemo(() => {
    if (!watch) {
      return [];
    }
    const steps = watch.map((key) => searchParams.get(key)) as string[];
    return steps;
  }, [watch, searchParams]);
  const onStepsChanges = useMethod(() => {
    onStep && onStep(steps);
  });
  useEffect(() => {
    onStepsChanges();
  }, [steps, onStepsChanges]);
  return {
    steps,
    setStep: updateSearchParams,
  };
}
