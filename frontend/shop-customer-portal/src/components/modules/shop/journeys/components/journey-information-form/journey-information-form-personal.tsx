"use client";

import { useFieldArray, useFormContext } from "react-hook-form";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Delete } from "@arthursenno/lizenzero-ui-react/Icon";

import { Divider } from "@/components/_common/divider";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { InformationTooltip, InformationTooltipDescription } from "@/components/_common/information-tooltip";

import { JourneyInformationFormData } from "./journey-information-form-provider";
import { PhoneInput } from "@/components/_common/forms/phone-input";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { FormInputIcon } from "@/components/_common/input-status-icon";

export function JourneyInformationFormPersonal() {
  const form = useFormContext<JourneyInformationFormData>();

  const t = useTranslations("shop.common.journey.information.personal");
  const { isUpdatingCart } = useShoppingCart();

  const phoneField = useFieldArray({
    name: "phones",
    control: form.control,
  });

  const customerSalutation = form.watch("salutation");
  const customerPhone = form.watch("phone");
  const customerMobile = form.watch("mobile");
  const customerPhones = form.watch("phones");

  if (form.formState.isSubmitting) return <JourneyInformationFormPersonalSkeleton />;

  const errors = form.formState.errors;

  return (
    <div className=" w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex items-center gap-2 mb-2">
        <p className="text-primary font-bold text-2xl">{t("title")}</p>
        <InformationTooltip>
          <InformationTooltipDescription>{t("description")}</InformationTooltipDescription>
        </InformationTooltip>
      </div>
      <p className="text-[#808FA9] font-light text-sm mb-8">*{t("mandatoryFields")}</p>
      <div className="space-y-6 w-full">
        <div>
          <p className="text-primary mb-2">{t("salutation.label")}</p>
          <div className="flex flex-wrap gap-4">
            <CustomRadio
              checked={customerSalutation === t("salutation.masculine")}
              onChange={() => form.setValue("salutation", t("salutation.masculine"))}
              label={t("salutation.masculine")}
              disabled={isUpdatingCart}
            />
            <CustomRadio
              checked={customerSalutation === t("salutation.feminine")}
              onChange={() => form.setValue("salutation", t("salutation.feminine"))}
              label={t("salutation.feminine")}
              disabled={isUpdatingCart}
            />
            <CustomRadio
              checked={customerSalutation === t("salutation.preferNotToSay")}
              onChange={() => form.setValue("salutation", t("salutation.preferNotToSay"))}
              label={t("salutation.preferNotToSay")}
              disabled={isUpdatingCart}
            />
          </div>
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            {...form.register("firstName")}
            label={t("firstName.label") + " *"}
            placeholder={t("firstName.placeholder")}
            variant={errors.firstName && "error"}
            errorMessage={errors.firstName?.message}
            rightIcon={<FormInputIcon control={form.control} name="firstName" />}
            enabled={!isUpdatingCart}
          />
          <Input
            {...form.register("surname")}
            label={t("surname.label") + " *"}
            placeholder={t("surname.placeholder")}
            variant={errors.surname && "error"}
            errorMessage={errors.surname?.message}
            rightIcon={<FormInputIcon control={form.control} name="surname" />}
            enabled={!isUpdatingCart}
          />
        </div>
        <div className="grid lg:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <p className="text-primary">{t("phone.label")} *</p>
            <PhoneInput
              name="phone"
              defaultValue={customerPhone}
              valueSetter={(value) => {
                value ? form.setValue("phone", value) : form.resetField("phone", { keepError: true, defaultValue: "" });
              }}
              errorSetter={(valid) =>
                valid ? form.clearErrors("phone") : form.setError("phone", { message: "Invalid phone number." })
              }
              isError={!!errors.phone}
              disabled={isUpdatingCart}
            />
            {!!errors.phone && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.phone.message}
                </span>
              </div>
            )}
          </div>
          <div className="space-y-2">
            <p className="text-primary">{t("mobile.label")}</p>
            <PhoneInput
              name="mobile"
              defaultValue={customerMobile}
              valueSetter={(value) => {
                form.setValue("mobile", value);

                if (!value) form.resetField("mobile", { keepError: true, defaultValue: "" });
              }}
              errorSetter={(valid) =>
                valid ? form.clearErrors("mobile") : form.setError("mobile", { message: "Invalid phone number." })
              }
              isError={!!errors.mobile}
              required={false}
              disabled={isUpdatingCart}
            />
            {!!errors.mobile && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.mobile.message}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      <Divider />

      <div className="space-y-6 w-full">
        <div className="flex items-center gap-2">
          <p className="text-primary">{t("otherPhones.label")}</p>
          <InformationTooltip className="fill-primary">
            <InformationTooltipDescription>{t("otherPhones.description")}</InformationTooltipDescription>
          </InformationTooltip>
        </div>

        {phoneField.fields.map((field, index) => {
          return (
            <div className="flex gap-6 items-center" key={field.id}>
              <div className="lg:w-1/2 w-full">
                <div className="space-y-2">
                  <p className="text-primary">{t("phone.label")}</p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <PhoneInput
                        name={`phones.${index}`}
                        defaultValue={customerPhones[index].phone_number}
                        valueSetter={(value) => {
                          form.setValue(`phones.${index}.phone_number`, value || "");

                          if (!value)
                            form.resetField(`phones.${index}.phone_number`, { keepError: true, defaultValue: "" });
                        }}
                        errorSetter={(valid) =>
                          valid
                            ? form.clearErrors(`phones.${index}.phone_number`)
                            : form.setError(`phones.${index}.phone_number`, { message: "Invalid phone number." })
                        }
                        isError={Boolean(!!errors.phones && errors.phones[index])}
                        disabled={isUpdatingCart}
                      />
                      <Button
                        color="dark-blue"
                        size="iconSmall"
                        variant="text"
                        trailingIcon={<Delete style={{ fill: "inherit" }} />}
                        type="button"
                        onClick={() => phoneField.remove(index)}
                        disabled={isUpdatingCart}
                      />
                    </div>
                    {!!errors.phones && errors.phones[index] && (
                      <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                        <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
                          {errors.phones[index]!.message}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <button
        type="button"
        onClick={() => phoneField.append({ phone_number: "" })}
        className="cursor-pointer font-medium mt-4 text-support-blue hover:text-support-blue/70"
      >
        {t("otherPhones.button")}
      </button>
    </div>
  );
}

export function JourneyInformationFormPersonalSkeleton() {
  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7 gap-5">
      <div className="flex">
        <p className="text-primary font-medium text-xl mb-2">Personal Data</p>
      </div>
      <div className="space-y-6 w-full">
        <div className="w-full">
          <Skeleton className="h-4 w-16 mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <Skeleton className="h-px w-full" />

        <div className="space-y-4">
          <Skeleton className="h-4 w-64" />
          <div className="flex gap-6 items-center">
            <div className="lg:w-1/2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
