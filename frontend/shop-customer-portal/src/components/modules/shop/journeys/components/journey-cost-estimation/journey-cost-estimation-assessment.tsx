"use client";

import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { memo, PropsWithChildren, useCallback, useEffect } from "react";
import {
  CostEstimationCountryTabs,
  CostEstimationLayout,
  CostEstimationNote,
  CostEstimationLayoutLeft,
  CostEstimationLayoutRight,
  CostEstimationBanner,
  useSearchParamsSteps,
} from "./common";
import {
  Root as AccordionRoot,
  AccordionItem,
  AccordionHeader,
  AccordionTrigger,
  AccordionContent,
} from "@radix-ui/react-accordion";
import { CostEstimationServiceTypeAssessment } from "./components/cost-estimation-service-type-assessment";
import { CostEstimationOtherCosts } from "./components/cost-estimation-other-cost";
import { CostEstimationMock } from "./mock";

interface CostEstimationAssessmentProps
  extends Pick<
    CostEstimationMock,
    | "countries"
    | "selectedCountry"
    | "setSelectedCountry"
    | "assessment"
    | "submitPackagingServicesCommitments"
    | "submitOtherCostsCommitments"
    | "onFractionsChange"
  > {
  onToResult: () => void;
}
export const CostEstimationAssessment = memo(function CostEstimationAssessment(props: CostEstimationAssessmentProps) {
  const {
    countries,
    setSelectedCountry,
    assessment: { packagingServices, packagingServicesCommitments, otherCostsCommitments },
    submitPackagingServicesCommitments,
    submitOtherCostsCommitments,
    onFractionsChange,
    onToResult,
  } = props;
  const t = useTranslations("shop.longJourney.costEstimation");

  const {
    steps: [country, accordionOpen],
    setStep,
  } = useSearchParamsSteps(["country", "accordionOpen"], ([country]) => {
    const exists = countries.find((c) => c.country_code === country);
    if (!exists) {
      setStep({ country: countries[0].country_code });
      return;
    }
  });

  const handleCountrySelect = (country: string) => {
    setStep({ country });
  };

  useEffect(() => {
    country && setSelectedCountry(country);
  }, [country, setSelectedCountry]);

  const accordionDefaultValue = accordionOpen ? accordionOpen.split(",") : ["0"];
  const handleAccordionChange = useCallback(
    (value: string[]) => {
      setStep({ accordionOpen: value.join(",") });
    },
    [setStep]
  );

  return (
    <CostEstimationLayout
      title={t("assessment.title")}
      description={t("assessment.description")}
      header={
        <CostEstimationCountryTabs
          selectedCountry={country}
          onCountrySelect={handleCountrySelect}
          countries={countries}
        />
      }
    >
      <CostEstimationLayoutLeft>
        <CostEstimationNote note={t("main.note")} />
        {/* Assessment sections */}
        <AccordionRoot
          type="multiple"
          defaultValue={accordionDefaultValue}
          onValueChange={handleAccordionChange}
          className="flex flex-col gap-6"
        >
          {packagingServices.map((service, serviceIndex) => {
            // Filter commitments for this specific service
            const serviceCommitments = packagingServicesCommitments.filter((commitment) =>
              commitment.title.includes(`"${service.name}"`)
            );

            return (
              <CostEstimationAccordionItem key={service.id} value={`${serviceIndex}`} title={service.name}>
                <CostEstimationServiceTypeAssessment
                  commitments={serviceCommitments}
                  packagingService={service}
                  onSubmitCommitments={submitPackagingServicesCommitments}
                  onFractionsChange={onFractionsChange}
                />
              </CostEstimationAccordionItem>
            );
          })}

          <CostEstimationAccordionItem value="other-costs" title="Other costs">
            <CostEstimationOtherCosts commitments={otherCostsCommitments} onSubmit={submitOtherCostsCommitments} />
          </CostEstimationAccordionItem>
        </AccordionRoot>
      </CostEstimationLayoutLeft>
      <CostEstimationLayoutRight>
        <CostEstimationBanner />
        <Button
          className="w-full space-y-6"
          color={"yellow"}
          variant="filled"
          size="medium"
          onClick={onToResult}
          type="button"
        >
          {t("assessment.submitLabel")}
        </Button>
      </CostEstimationLayoutRight>
    </CostEstimationLayout>
  );
});

type CostEstimationAccordionItemProps = PropsWithChildren<{
  title: string;
  value: string;
}>;
const CostEstimationAccordionItem = memo(function CostEstimationAccordionItem(props: CostEstimationAccordionItemProps) {
  const { children, title, value } = props;
  return (
    <AccordionItem value={value} className="bg-tonal-dark-cream-96 rounded-[40px]">
      <AccordionHeader className="px-10 py-6">
        <AccordionTrigger asChild>
          <button className="w-full text-primary text-2xl font-bold text-left flex items-center justify-between group focus:outline-none focus-visible:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg">
            <span>{title}</span>
            <KeyboardArrowDown className="size-8 fill-primary group-data-[state=open]:rotate-180" aria-hidden="true" />
          </button>
        </AccordionTrigger>
      </AccordionHeader>
      <AccordionContent className="px-10 pt-6 pb-10 border-t border-t-tonal-dark-cream-80">{children}</AccordionContent>
    </AccordionItem>
  );
});
