"use client";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useFormContext } from "react-hook-form";

export function JourneyInformationFormSubmit() {
  const {
    formState: { isSubmitting, errors, isSubmitted },
  } = useFormContext();
  const t = useTranslations("shop.common.journey.information.submit");
  const { isUpdatingCart } = useShoppingCart();

  const hasErrors = !!Object.keys(errors).length;

  const buttonColor = hasErrors ? "red" : "yellow";
  const disabled = isSubmitting || isUpdatingCart;
  const label = isSubmitting ? t("button.loading") : t("button.label");

  return (
    <Button
      type="submit"
      color={buttonColor}
      disabled={disabled}
      variant="filled"
      size="large"
      className="w-full"
      form="journey-information-form"
      trailingIcon={!isSubmitting && <East />}
    >
      {label}
    </Button>
  );
}
