"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { InformationTooltip, InformationTooltipDescription } from "@/components/_common/information-tooltip";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { Combobox } from "@/components/ui/combobox";
import { Skeleton } from "@/components/ui/skeleton";
import { useCustomer } from "@/hooks/use-customer";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { validateVatId } from "@/lib/api/company";
import { COUNTRIES, EU_COUNTRY_CODES } from "@/utils/consts/countries";
import { useVatErrorTranslator } from "@/utils/vat-error-translator";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useMutation } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { JourneyInformationFormData } from "./journey-information-form-provider";

export function JourneyInformationFormCompany() {
  const form = useFormContext<JourneyInformationFormData>();

  const t = useTranslations("shop.common.journey.information.company");
  const globalT = useTranslations("global");
  const { shoppingCart, updateCart } = useShoppingCart();
  const { customer } = useCustomer();
  const getVatApiErrorMessage = useVatErrorTranslator();

  const companyName = useWatch({ control: form.control, name: "companyName" });
  const companyCountryCode = useWatch({ control: form.control, name: "countryCode" });
  const companyCity = useWatch({ control: form.control, name: "city" });
  const companyZipCode = useWatch({ control: form.control, name: "zipCode" });
  const companyStreetAndNumber = useWatch({ control: form.control, name: "streetAndNumber" });
  const documentType = useWatch({ control: form.control, name: "documentType" });

  function handleSelectCountry(country: { value: string; label: string } | null) {
    form.setValue("city", "");
    form.setValue("zipCode", "");
    form.setValue("streetAndNumber", "");
    form.setValue("countryCode", "");
    form.setValue("vatId", "");
    form.setValue("taxNumber", "");

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    const isEuCountry = EU_COUNTRY_CODES.find((code) => code === foundCountry.code);

    form.setValue("countryCode", foundCountry.code);
    form.clearErrors("city");
    form.clearErrors("zipCode");
    form.clearErrors("streetAndNumber");
    form.clearErrors("countryCode");
    form.clearErrors("vatId");
    form.clearErrors("taxNumber");

    const documentType = isEuCountry ? "VAT" : "TAX";
    setDocumentType(documentType);

    if (!isEuCountry) {
      form.clearErrors("vatId");
      enqueueSnackbar(t("vat.noVatCharge"), { variant: "info" });
      if (shoppingCart.vat_percentage !== 0) updateCart({ vat_percentage: 0 });
      return;
    }

    enqueueSnackbar(t("vat.vatCharge"), { variant: "info" });
    if (shoppingCart.vat_percentage !== 19) updateCart({ vat_percentage: 19 });
    form.clearErrors("vatId");
  }

  const validateVatMutation = useMutation({
    mutationFn: async () => {
      const { vatId, countryCode, companyName, zipCode, city, streetAndNumber } = form.getValues();

      if (!vatId || !countryCode || !companyName || !zipCode || !city || !streetAndNumber) return;

      if (errors.vatId?.type === "invalid_string") return;

      await form.trigger("vatId");

      const formattedVatId = vatId.replace(/\s/g, "").trim();

      const vatValidationResponse = await validateVatId({
        vat_id: formattedVatId,
        country_code: countryCode,
        company_name: companyName,
        company_zipcode: companyZipCode,
        company_city: companyCity,
        company_street: companyStreetAndNumber,
      });

      if (!vatValidationResponse?.is_valid) {
        // Use the helper function to get the translated error message
        const errorMessage = vatValidationResponse?.error
          ? getVatApiErrorMessage(vatValidationResponse.error)
          : globalT("inputs.vatId.errors.invalid");

        form.setError("vatId", { message: errorMessage });

        if (shoppingCart.vat_percentage !== 19 && isEuCountry) {
          enqueueSnackbar(t("vat.vatCharge"), { variant: "info" });
          updateCart({ vat_percentage: 19 });
          return;
        }

        return;
      }

      form.setValue("vatId", formattedVatId);
      form.clearErrors("vatId");

      if (shoppingCart.vat_percentage !== 0 && countryCode !== "DE") {
        enqueueSnackbar(t("vat.noVatCharge"), { variant: "info" });
        updateCart({ vat_percentage: 0 });
      }
    },
  });

  async function setDocumentType(type: "VAT" | "TAX") {
    if (validateVatMutation.isPending) return;

    form.resetField("vatId");
    form.resetField("taxNumber");
    form.clearErrors("vatId");
    form.clearErrors("taxNumber");
    form.setValue("vatId", undefined);
    form.setValue("taxNumber", undefined);
    form.setValue("documentType", type);

    if (type === "TAX") {
      const isEuCountry = EU_COUNTRY_CODES.find((code) => code === companyCountryCode);

      if (isEuCountry && shoppingCart.vat_percentage !== 19) updateCart({ vat_percentage: 19 });
    }
  }

  const emailField = useFieldArray({
    name: "emails",
    control: form.control,
    keyName: "key",
  });

  if (!customer) return null;

  const isTaxAndVatEnabled =
    !!companyCountryCode && !!companyName && !!companyZipCode && !!companyCity && !!companyStreetAndNumber;

  const isEuCountry = EU_COUNTRY_CODES.find((code) => code === companyCountryCode);

  if (form.formState.isSubmitting) return <JourneyInformationFormCompanySkeleton />;

  const errors = form.formState.errors;
  const selectedCountry = COUNTRIES.find((c) => c.code === companyCountryCode);

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex items-center gap-2 mb-2">
        <p className="text-primary font-bold text-2xl">{t("title")}</p>
        <InformationTooltip>
          <InformationTooltipDescription>{t("description")}</InformationTooltipDescription>
        </InformationTooltip>
      </div>
      <p className="text-[#808FA9] font-light text-sm mb-8">*{globalT("validation.mandatoryFields")}</p>
      <div className="space-y-6 w-full">
        <div className="w-full ">
          <Input
            {...form.register("companyName")}
            label={`${t("companyName.label")} *`}
            placeholder={t("companyName.placeholder")}
            errorMessage={errors.companyName?.message}
            rightIcon={<FormInputIcon control={form.control} name="companyName" />}
            variant={errors.companyName && "error"}
          />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <p className="text-primary">{t("country.label")} *</p>
            <div className="h-10 flex items-center gap-4">
              {selectedCountry ? (
                <CountryIcon
                  country={{ name: selectedCountry.name, flag_url: selectedCountry.flag_url }}
                  className="size-6 rounded-full"
                />
              ) : (
                <div className="size-6 rounded-full bg-surface-03" />
              )}
              <p className="text-primary text-base">{selectedCountry?.name || t("country.placeholder")}</p>
              <button
                type="button"
                className="text-sm text-support-blue font-bold cursor-not-allowed"
                aria-disabled="true"
                tabIndex={-1}
              >
                {globalT("words.change")}
              </button>
            </div>
            {!!errors.countryCode && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.countryCode.message}
                </span>
              </div>
            )}
          </div>
          <Input
            {...form.register("city")}
            label={`${t("city.label")} *`}
            placeholder={t("city.placeholder")}
            rightIcon={<FormInputIcon control={form.control} name="city" />}
            errorMessage={errors.city?.message}
            variant={errors.city && "error"}
            enabled={!!companyCountryCode}
          />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            {...form.register("streetAndNumber")}
            label={`${t("streetAndNumber.label")} *`}
            placeholder={t("streetAndNumber.placeholder")}
            rightIcon={<FormInputIcon control={form.control} name="streetAndNumber" />}
            errorMessage={errors.streetAndNumber?.message}
            variant={errors.streetAndNumber && "error"}
            enabled={!!companyCountryCode}
          />
          <Input
            {...form.register("zipCode")}
            label={`${t("zipCode.label")} *`}
            placeholder={t("zipCode.placeholder")}
            errorMessage={errors.zipCode?.message}
            rightIcon={<FormInputIcon control={form.control} name="zipCode" />}
            variant={errors.zipCode && "error"}
            enabled={!!companyCountryCode}
          />
        </div>
        <div className="w-full gap-8">
          <Input
            {...form.register("additionalAddressLine", {
              setValueAs: (value) => value || undefined,
            })}
            label={`${t("additionalAddressLine.label")}`}
            placeholder={t("additionalAddressLine.placeholder")}
            errorMessage={errors.additionalAddressLine?.message}
            rightIcon={<FormInputIcon control={form.control} name="additionalAddressLine" />}
            variant={errors.additionalAddressLine && "error"}
            enabled={!!companyCountryCode}
          />
        </div>
        <div className="w-full">
          <div className="flex items-center gap-6">
            <CustomRadio
              checked={documentType === "VAT"}
              label={`${globalT("inputs.vatId.label")} *`}
              onChange={() => setDocumentType("VAT")}
              disabled={!isTaxAndVatEnabled}
            />
            <CustomRadio
              checked={documentType === "TAX"}
              onChange={() => setDocumentType("TAX")}
              label={`${globalT("inputs.taxNumber.label")} *`}
              disabled={!isTaxAndVatEnabled}
            />
          </div>
          {documentType === "VAT" ? (
            <Input
              {...form.register("vatId", {
                onBlur: (event) => validateVatMutation.mutate(),
              })}
              label=""
              placeholder={globalT("inputs.vatId.label")}
              enabled={isTaxAndVatEnabled}
              variant={errors.vatId ? "error" : "enabled"}
              rightIcon={<FormInputIcon control={form.control} name="vatId" loading={validateVatMutation.isPending} />}
              errorMessage={errors.vatId?.message}
            />
          ) : (
            <>
              <Input
                {...form.register("taxNumber")}
                label=""
                placeholder={globalT("inputs.taxNumber.label")}
                errorMessage={errors.taxNumber?.message}
                enabled={isTaxAndVatEnabled}
                variant={errors.taxNumber ? "error" : "enabled"}
                rightIcon={<FormInputIcon control={form.control} name="taxNumber" />}
              />
              {isEuCountry && companyCountryCode !== "DE" && isTaxAndVatEnabled && (
                <p className="text-success text-sm mt-2">{t("vat.enterVatId")}</p>
              )}
            </>
          )}
        </div>
      </div>
      <Divider />
      <div className="space-y-6 w-full">
        <div className="flex items-center gap-2">
          <p className="text-primary">{t("emails.title")}</p>
          <InformationTooltip>
            <InformationTooltipDescription>{t("emails.description")}</InformationTooltipDescription>
          </InformationTooltip>
        </div>

        {emailField.fields.map((field, index) => (
          <div className="flex gap-6 items-center" key={`${index}`}>
            <div className="w-full">
              <div className="space-y-2">
                <div className="flex items-center gap-4">
                  <Input
                    {...form.register(`emails.${index}.email`, {
                      onBlur: (event) => emailField.update(index, { email: event.target.value }),
                    })}
                    defaultValue={field.email}
                    label={t("email.label")}
                    placeholder={t("email.placeholder")}
                    type="email"
                    variant={!!errors.emails && errors.emails[index] ? "error" : "enabled"}
                    rightIcon={<FormInputIcon control={form.control} name={`emails.${index}.email`} />}
                  />
                  <Button
                    color="dark-blue"
                    size="iconSmall"
                    variant="text"
                    className="mt-8"
                    trailingIcon={<Delete style={{ fill: "inherit" }} />}
                    type="button"
                    onClick={() => emailField.remove(index)}
                  />
                </div>
                {!!errors.emails && errors.emails[index] && (
                  <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                    <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                      {errors.emails[index]!.message}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      <p
        onClick={() => emailField.append({ email: "" })}
        className="cursor-pointer font-medium mt-4 text-support-blue hover:text-support-blue/70"
      >
        {t("emails.button")}
      </p>
    </div>
  );
}

export function JourneyInformationFormCompanySkeleton() {
  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7 gap-5">
      <div className="flex">
        <p className="text-primary font-medium text-xl mb-2">Company information</p>
      </div>
      <div className="space-y-6 w-full">
        <div className="w-full">
          <Skeleton className="h-4 w-16 mb-2" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <Skeleton className="h-px w-full" />

        <div className="space-y-4">
          <Skeleton className="h-4 w-64" />
          <div className="flex gap-6 items-center">
            <div className="lg:w-1/2 w-full">
              <Skeleton className="h-10 w-full" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
