"use client";

import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useTranslations } from "next-intl";
import { CostEstimationCommitment, useSearchParamsSteps } from "../../common";
import { CostEstimationAssessmentCommitmentQuestion } from "../cost-estimation-assessment-commitment";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { cn } from "@/lib/utils";
import { Check } from "@arthursenno/lizenzero-ui-react/Icon";

interface CostEstimationOtherCostsProps {
  commitments: CostEstimationCommitment[];
  onSubmit: (commitments: CostEstimationCommitment[]) => void;
}
export const CostEstimationOtherCosts = memo(function CostEstimationOtherCosts(props: CostEstimationOtherCostsProps) {
  const { commitments, onSubmit } = props;
  const [data, setData] = useState(commitments || []);

  // reset
  useEffect(() => {
    setData(commitments);
    setCompleted(commitments.every((c) => typeof c.answer !== "undefined"));
  }, [commitments]);

  const {
    steps: [otherCostsCompleted],
    setStep,
  } = useSearchParamsSteps(["otherCostsCompleted"]);

  const [completed, setCompleted] = useState(otherCostsCompleted === "true");

  useEffect(() => {
    setStep({ otherCostsCompleted: completed.toString() as "true" });
  }, [completed, setStep]);
  const onChange = useCallback((target: CostEstimationCommitment) => {
    setData((data) =>
      data.reduce((data, commitment) => [...data, commitment.id === target.id ? target : commitment], [] as typeof data)
    );
    setCompleted(false);
  }, []);
  const onContinue = useCallback(() => {
    onSubmit(data);
    setCompleted(true);
  }, [data, onSubmit]);
  const canContinue = useMemo(() => data.every((a) => typeof a.answer !== "undefined"), [data]);
  const t = useTranslations("shop.longJourney.costEstimation");

  return (
    <div className="flex flex-col gap-10">
      {data.map((commitment) => {
        return (
          <CostEstimationAssessmentCommitmentQuestion key={commitment.id} commitment={commitment} onChange={onChange} />
        );
      })}
      <Button
        className={cn("self-start", completed && "!bg-light-green !text-dark-green")}
        color="dark-blue"
        variant="filled"
        size="medium"
        onClick={onContinue}
        type="button"
        disabled={completed || !canContinue}
      >
        {t(
          completed
            ? "assessment.estimation.otherCosts.submitLabelCompleted"
            : "assessment.estimation.otherCosts.submitLabel"
        )}
        {completed && <Check className="ml-2 size-5 fill-dark-green" />}
      </Button>
    </div>
  );
});
