"use client";

import { InformationTooltip, InformationTooltipDescription } from "@/components/_common/information-tooltip";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { PasswordInput } from "@/components/ui/password-input";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useFormContext, useWatch } from "react-hook-form";
import { useTranslations } from "next-intl";
import { Skeleton } from "@/components/ui/skeleton";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { JourneyInformationFormData } from "./journey-information-form-provider";

export function JourneyInformationFormAccount() {
  const form = useFormContext<JourneyInformationFormData>();
  const t = useTranslations("shop.common.journey.information.account");
  const globalT = useTranslations("global");
  const { isUpdatingCart } = useShoppingCart();

  const password = useWatch({ control: form.control, name: "password" });
  const confirmPassword = useWatch({ control: form.control, name: "confirmPassword" });
  const email = useWatch({ control: form.control, name: "email" });

  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;

  if (form.formState.isSubmitting) return <JourneyInformationFormAccountSkeleton />;

  const errors = form.formState.errors;

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex items-center gap-2 mb-2">
        <p className="text-primary font-bold text-2xl">{t("title")}</p>
        <InformationTooltip>
          <InformationTooltipDescription>{t("description")}</InformationTooltipDescription>
        </InformationTooltip>
      </div>
      <p className="text-[#808FA9] font-light text-sm mb-8">*{globalT("validation.mandatoryFields")}</p>

      <div className="space-y-6 w-full">
        <Input
          {...form.register("email")}
          label={t("email.label")}
          placeholder={t("email.placeholder")}
          type="email"
          variant={errors.email && "error"}
          errorMessage={errors.email?.message}
          rightIcon={<FormInputIcon control={form.control} name="email" />}
          enabled={false}
          defaultValue="<EMAIL>" // TODO: Mock data for testing
          // value={email}
        />

        <PasswordInput
          {...form.register("password")}
          label={`${t("password.label")} *`}
          placeholder={t("password.placeholder")}
          variant={errors.password && "error"}
          errorMessage={errors.password?.message}
          enabled={!isUpdatingCart}
        />

        <PasswordInput
          {...form.register("confirmPassword")}
          label={`${t("confirmPassword.label")} *`}
          placeholder={t("confirmPassword.placeholder")}
          variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
          errorMessage={
            (errors.confirmPassword && errors.confirmPassword.message) ||
            (isNotValidConfirmPassword && t("confirmPassword.errors.mismatch"))
          }
          enabled={!isUpdatingCart}
        />

        <PasswordStrengthBar password={password} />
      </div>
    </div>
  );
}

export function JourneyInformationFormAccountSkeleton() {
  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7 gap-5">
      <div className="flex">
        <p className="text-primary font-medium text-xl mb-2">Account data</p>
      </div>
      <div className="space-y-6 w-full">
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <Skeleton className="h-10 w-full" />
        </div>
        <Skeleton className="h-4 w-full" />
      </div>
    </div>
  );
}
