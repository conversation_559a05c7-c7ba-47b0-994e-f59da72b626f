"use client";

import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { JourneyShoppingCartCard } from "@/components/modules/shop/journeys/components/journey-shopping-cart-card";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { memo, useCallback, useMemo } from "react";
import {
  CostEstimationBanner,
  CostEstimationLayout,
  CostEstimationLayoutLeft,
  CostEstimationLayoutRight,
  CostEstimationNote,
  CostEstimationResult,
  useSearchParamsSteps,
} from "./common";
import { CountryIcon } from "@/components/_common/country-icon";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/formatCurrency";

interface CostEstimationResultsProps {
  onToAssessment: () => void;
  data: CostEstimationResult[];
}
export const CostEstimationResults = memo(function CostEstimationResults(props: CostEstimationResultsProps) {
  const { onToAssessment, data } = props;
  const t = useTranslations("shop.longJourney.costEstimation");
  const onToDetails = useCallback(() => {
    location.href = "./informations";
  }, []);
  return (
    <CostEstimationLayout title={t("title")} description={t("main.description")}>
      <CostEstimationLayoutLeft>
        <CostEstimationNote note={t("main.note")} />
        <div className="p-10 bg-tonal-dark-cream-96 rounded-[40px]">
          <p className="text-primary text-2xl font-bold">{t("main.estimation.title")}</p>
          <p className="text-tonal-dark-cream-30 text-base mt-5">{t("main.estimation.description")}</p>
          <Button
            className="w-full mt-8"
            trailingIcon={<East />}
            variant="outlined"
            color="dark-blue"
            size="small"
            onClick={onToAssessment}
          >
            {t("main.estimation.btnLabel")}
          </Button>
          <div className="flex flex-col gap-4 mt-8">
            {data.map((item) => (
              <CostEstimationResultCard key={item.country.country_code} data={item} />
            ))}
          </div>
        </div>
      </CostEstimationLayoutLeft>
      <CostEstimationLayoutRight>
        <CostEstimationBanner />
        <JourneyShoppingCartCard defaultLayout="mini" />
        <Button
          className="w-full"
          trailingIcon={<East />}
          color={"yellow"}
          variant="filled"
          size="medium"
          onClick={onToDetails}
          type="button"
        >
          {t("main.submitLabel")}
        </Button>
      </CostEstimationLayoutRight>
    </CostEstimationLayout>
  );
});

interface CostEstimationResultCardProps {
  data: CostEstimationResult;
}
const CostEstimationResultCard = memo(function CostEstimationResultCard(props: CostEstimationResultCardProps) {
  const {
    data: { country, items },
  } = props;
  const t = useTranslations("shop.longJourney.costEstimation");
  const { setStep } = useSearchParamsSteps();
  const toAddInfo = useCallback(() => {
    setStep({ step: "assessment", country: country.country_code, serviceStep: "calculator" });
  }, [setStep, country]);
  const estimatedCosts = useMemo(() => {
    return items.reduce(
      (total, item) => {
        if (item.furtherInformationRequired || total === null || typeof item.price !== "number") {
          return null;
        }
        return total + item.price;
      },
      0 as number | null
    );
  }, [items]);
  return (
    <div className="flex flex-col gap-4 bg-background rounded-[32px] px-7 py-5">
      <div className="flex flex-row gap-3 items-center">
        <CountryIcon
          country={{
            flag_url: country.country_flag,
            name: country.country_name,
          }}
          className={cn("size-7")}
        />
        <span className="font-bold text-primary text-xl leading-6">{country.country_name}</span>
      </div>
      <div className="flex flex-col gap-4">
        {items.map(({ name, price, furtherInformationRequired }) => {
          return (
            <div key={name} className="flex flex-row justify-between items-center">
              <span className="text-lg leading-6 text-tonal-dark-cream-20">{name}</span>
              {furtherInformationRequired ? (
                <Button className="" color="light-blue" variant="text" size="small" onClick={toAddInfo} type="button">
                  {"Further information required"}
                </Button>
              ) : (
                <span className="text-lg leading-6 text-primary">{formatCurrency(price)}</span>
              )}
            </div>
          );
        })}
      </div>
      <hr className="text-tonal-dark-cream-80 w-full justify-start" />
      <div className="flex flex-row justify-between items-center">
        <span className="text-lg leading-6 text-tonal-dark-cream-20 font-bold">{t("results.estimatedCosts")}</span>
        <span className="text-lg leading-6 text-primary font-bold">
          {estimatedCosts === null ? "——" : formatCurrency(estimatedCosts)}
        </span>
      </div>
    </div>
  );
});
