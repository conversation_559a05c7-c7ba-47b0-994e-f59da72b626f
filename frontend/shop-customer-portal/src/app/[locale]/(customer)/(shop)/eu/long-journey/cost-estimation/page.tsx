"use client";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { useTranslations } from "next-intl";
import { Icons } from "@/components/ui/icons";
import { ShopLongJourneyStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyCostEstimation } from "@/components/modules/shop/journeys/components/journey-cost-estimation";

export default function CostEstimation() {
  const t = useTranslations("shop.longJourney.costEstimation");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Icons.calculatorQuestionIcon} />
      <ShopLongJourneyStepper stepById={"estimate-your-costs"} />
      <ShopContent>
        <JourneyCostEstimation />
      </ShopContent>
    </>
  );
}
