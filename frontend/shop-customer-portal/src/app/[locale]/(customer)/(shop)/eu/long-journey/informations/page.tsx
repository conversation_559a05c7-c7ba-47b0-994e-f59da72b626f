"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { ResetPasswordModal } from "@/components/_common/modals/reset-password-modal";
import { Padlock } from "@arthursenno/lizenzero-ui-react/Icon";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLongJourneyStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyInformationForm } from "@/components/modules/shop/journeys/components/journey-information-form";
import { JourneyInformationFormProvider } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-provider";
import { JourneyInformationFormSubmit } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-submit";
import { ShopBanner, IconBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { SelectCountriesMapModal } from "@/components/_common/modals/select-countries-map-modal";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { JourneyShoppingCartCard } from "@/components/modules/shop/journeys/components/journey-shopping-cart-card";
import { Icons } from "@/components/ui/icons";

import { useTranslations } from "next-intl";

export default function Information() {
  const t = useTranslations("shop.longJourney.information");
  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Icons.userProfileIcon} />
      <ShopLongJourneyStepper stepById={"enter-information"} />
      <ShopContent>
        <TitleAndSubTitle title="Enter your information" subTitle="You are purchasing our License Service" />
        {/* <SelectCountriesMapModal /> */}
        <JourneyInformationFormProvider>
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 w-full mb-20">
            <div className="lg:col-span-7">
              <JourneyInformationForm />
            </div>
            <div className="flex flex-col gap-6 lg:col-span-5 space-y-8 lg:space-y-16">
              <JourneyShoppingCartCard />
              <div className="flex flex-col gap-8">
                <div className="w-full">
                  <JourneyInformationFormSubmit />
                </div>
              </div>
              {/* <ShopBanner title="" style={{ width: "100%" }}>
                <IconBanner
                  className="text-white "
                  icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
                />
                <div className="">
                  <p className="font-bold text-base">{t("banner.title")}</p>
                  <span className="w-full text-sm ">{t("banner.description")}</span>
                </div>
              </ShopBanner> */}
            </div>
          </div>
        </JourneyInformationFormProvider>
      </ShopContent>
      <ResetPasswordModal />
    </>
  );
}
