import { getCustomerByUserId } from "@/lib/api/customer";
import { queryClient } from "@/lib/react-query";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

const MOCK_CUSTOMER_RESULT = Object.freeze({
  customer: {
    id: 1,
    user_id: 1,
    type: "REGULAR",
    first_name: "Test",
    last_name: "User",
    salutation: "Mr",
    email: "<EMAIL>",
    is_active: true,
    company_name: "Test Company",
    language: "en",
    currency: "EUR",
    created_at: "2024-01-01T10:00:00Z",
    updated_at: "2024-01-15T14:30:00Z",
    deleted_at: null,
    phones: [
      {
        id: 1,
        phone_number: "+49123456789",
        customer_id: 1,
        phone_type: "MOBILE",
      },
      {
        id: 2,
        phone_number: "+49987654321",
        customer_id: 1,
        phone_type: "WORK",
      },
    ],
    company: {
      id: 1,
      name: "Test Company",
      vat: "DE123456789",
      tin: null,
      lucid: null,
      description: null,
      website: "https://testcompany.com",
      emails: [
        { id: "1", email: "<EMAIL>" },
        { id: "2", email: "<EMAIL>" },
      ],
      address: {
        country_code: "DE",
        city: "Berlin",
        zip_code: "10115",
        street_and_number: "Test Street 123",
        additional_address: "",
        address_line: "Test Street 123, Berlin, DE, 10115",
      },
      billing: {
        full_name: "Test User",
        country_code: "DE",
        country_name: "Germany",
        company_name: "Test Company",
        street_and_number: "Test Street 123",
        city: "Berlin",
        zip_code: "10115",
        is_custom: false,
      },
      created_at: "2024-01-01T10:00:00Z",
      updated_at: "2024-01-15T14:30:00Z",
      deleted_at: null,
    },
    contracts: [
      {
        id: 1,
        customer_id: 1,
        type: "EU_LICENSE",
        status: "ACTIVE",
        title: "EU License Contract",
        start_date: "2024-01-01T00:00:00Z",
        end_date: "2024-12-31T23:59:59Z",
        termination_date: null,
        termination_id: null,
        termination: null,
        licenses: [],
        action_guides: [],
        files: [],
        general_informations: [],
      },
    ],
    coupons: [],
    commissions: [],
    hasActiveContract: true,
  },
  invalidateCustomer: () => {},
  isLoading: false,
});

export function useCustomer() {
  const session = useSession();

  const user = session.data?.user;

  const customerQuery = useQuery({
    queryKey: ["customer", user?.id],
    queryFn: async () => {
      const foundCustomer = await getCustomerByUserId(Number(user!.id));

      if (!foundCustomer) return null;

      const { companies, ...customer } = foundCustomer;

      return {
        ...customer,
        company: companies[0] || null,
        hasActiveContract: customer.contracts.some((contract) => contract.status === "ACTIVE"),
      };
    },
    enabled: !!user?.id,
  });

  function invalidateCustomer() {
    queryClient.invalidateQueries({ queryKey: ["customer", user?.id] });
  }

  return MOCK_CUSTOMER_RESULT;
  //End mock data block
  const customer = customerQuery.data || null;

  return {
    customer,
    invalidateCustomer,
    isLoading: customerQuery.isLoading,
  };
}
